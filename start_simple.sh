#!/bin/bash

# 简化版启动脚本 - 专门处理conda环境问题
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}🚀 SearXNG + MCP Server 简化启动脚本${NC}"
echo -e "${CYAN}====================================${NC}"

# 获取项目目录
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo -e "${BLUE}项目目录: $PROJECT_DIR${NC}"

# 第一步：初始化conda
echo -e "${YELLOW}🐍 步骤1: 初始化conda环境${NC}"

# 检查conda路径
CONDA_PATH=""
if [ -f ~/miniconda3/etc/profile.d/conda.sh ]; then
    CONDA_PATH="~/miniconda3/etc/profile.d/conda.sh"
    echo -e "${GREEN}找到conda: $CONDA_PATH${NC}"
elif [ -f ~/anaconda3/etc/profile.d/conda.sh ]; then
    CONDA_PATH="~/anaconda3/etc/profile.d/conda.sh"
    echo -e "${GREEN}找到conda: $CONDA_PATH${NC}"
else
    echo -e "${RED}❌ 未找到conda安装${NC}"
    echo -e "${YELLOW}请确保conda已安装${NC}"
    exit 1
fi

# 初始化conda
echo -e "${BLUE}初始化conda...${NC}"
source ~/miniconda3/etc/profile.d/conda.sh 2>/dev/null || source ~/anaconda3/etc/profile.d/conda.sh

# 验证conda
if command -v conda &> /dev/null; then
    echo -e "${GREEN}✅ conda初始化成功${NC}"
    conda --version
else
    echo -e "${RED}❌ conda初始化失败${NC}"
    exit 1
fi

# 第二步：检查searxng环境
echo -e "${YELLOW}🔍 步骤2: 检查searxng环境${NC}"

# 查找searxng环境路径
SEARXNG_ENV_PATH=""
if conda env list | grep -q "searxng"; then
    # 获取searxng环境的完整路径
    SEARXNG_ENV_PATH=$(conda env list | grep "searxng" | awk '{print $NF}')
    echo -e "${GREEN}✅ searxng环境存在: $SEARXNG_ENV_PATH${NC}"
else
    echo -e "${YELLOW}创建searxng环境...${NC}"
    conda create -n searxng python=3.11 -y
    SEARXNG_ENV_PATH=$(conda env list | grep "searxng" | awk '{print $NF}')
    echo -e "${GREEN}✅ searxng环境创建完成: $SEARXNG_ENV_PATH${NC}"
fi

# 第三步：激活环境
echo -e "${YELLOW}🔄 步骤3: 激活searxng环境${NC}"

# 使用完整路径激活环境
conda activate "$SEARXNG_ENV_PATH"

# 验证环境激活
if [[ "$CONDA_DEFAULT_ENV" == "searxng" ]]; then
    echo -e "${GREEN}✅ searxng环境激活成功${NC}"
    echo -e "${BLUE}当前环境: $CONDA_DEFAULT_ENV${NC}"
    echo -e "${BLUE}Python版本: $(python --version)${NC}"
else
    echo -e "${RED}❌ 环境激活失败${NC}"
    echo -e "${YELLOW}当前环境: $CONDA_DEFAULT_ENV${NC}"
    exit 1
fi

# 第四步：安装依赖
echo -e "${YELLOW}📦 步骤4: 检查依赖${NC}"

cd "$PROJECT_DIR"

# 安装SearXNG依赖
if [ -f "requirements.txt" ]; then
    echo -e "${BLUE}安装SearXNG依赖...${NC}"
    pip install -r requirements.txt --quiet
    echo -e "${GREEN}✅ SearXNG依赖安装完成${NC}"
fi

# 安装MCP依赖
if [ -f "enhanced_search_mcp/requirements.txt" ]; then
    echo -e "${BLUE}安装MCP依赖...${NC}"
    pip install -r enhanced_search_mcp/requirements.txt --quiet
    echo -e "${GREEN}✅ MCP依赖安装完成${NC}"
fi

# 第五步：清理旧进程
echo -e "${YELLOW}🛑 步骤5: 清理旧进程${NC}"

pkill -f "python.*searx.webapp" 2>/dev/null || true
pkill -f "python.*server.py" 2>/dev/null || true
sleep 2

# 清理端口
if lsof -Pi :8885 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo -e "${YELLOW}释放端口8885...${NC}"
    lsof -ti:8885 | xargs kill -9 2>/dev/null || true
fi

if lsof -Pi :8881 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo -e "${YELLOW}释放端口8881...${NC}"
    lsof -ti:8881 | xargs kill -9 2>/dev/null || true
fi

# 第六步：创建日志目录
echo -e "${YELLOW}📁 步骤6: 准备日志目录${NC}"
mkdir -p logs enhanced_search_mcp/logs

# 第七步：设置环境变量
echo -e "${YELLOW}🔧 步骤7: 设置环境变量${NC}"

export SEARXNG_PORT="8885"
export SEARXNG_BIND_ADDRESS="127.0.0.1"
export SEARXNG_URL="http://localhost:8885"
export SEARXNG_SECRET="$(openssl rand -hex 32)"  # 生成随机密钥
export MCP_HOST="0.0.0.0"
export MCP_PORT="8881"
export LOG_LEVEL="INFO"

echo -e "${BLUE}环境变量已设置:${NC}"
echo -e "  SEARXNG_PORT=$SEARXNG_PORT"
echo -e "  SEARXNG_BIND_ADDRESS=$SEARXNG_BIND_ADDRESS"
echo -e "  SEARXNG_SECRET=****(已设置)"
echo -e "  MCP_HOST=$MCP_HOST"
echo -e "  MCP_PORT=$MCP_PORT"

# 第八步：启动SearXNG
echo -e "${YELLOW}🔍 步骤8: 启动SearXNG${NC}"

nohup python -m searx.webapp > logs/searxng.log 2>&1 &
SEARXNG_PID=$!
echo -e "${GREEN}✅ SearXNG已启动 (PID: $SEARXNG_PID)${NC}"

# 等待SearXNG启动
echo -e "${BLUE}等待SearXNG启动...${NC}"
for i in {1..30}; do
    if curl -s --connect-timeout 2 http://localhost:8885 > /dev/null 2>&1; then
        echo -e "${GREEN}✅ SearXNG启动成功${NC}"
        break
    fi
    sleep 1
    echo -n "."
done

if ! curl -s --connect-timeout 2 http://localhost:8885 > /dev/null 2>&1; then
    echo -e "${RED}❌ SearXNG启动失败${NC}"
    echo -e "${YELLOW}查看日志: tail -20 logs/searxng.log${NC}"
    exit 1
fi

# 第九步：启动MCP Server
echo -e "${YELLOW}🤖 步骤9: 启动MCP Server${NC}"

cd enhanced_search_mcp
nohup python server.py > logs/mcp_server.log 2>&1 &
MCP_PID=$!
echo -e "${GREEN}✅ MCP Server已启动 (PID: $MCP_PID)${NC}"

# 等待MCP Server启动
echo -e "${BLUE}等待MCP Server启动...${NC}"
for i in {1..20}; do
    if curl -s --connect-timeout 2 http://localhost:8881/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ MCP Server启动成功${NC}"
        break
    fi
    sleep 1
    echo -n "."
done

if ! curl -s --connect-timeout 2 http://localhost:8881/health > /dev/null 2>&1; then
    echo -e "${RED}❌ MCP Server启动失败${NC}"
    echo -e "${YELLOW}查看日志: tail -20 logs/mcp_server.log${NC}"
    exit 1
fi

# 获取WSL IP
WSL_IP=$(hostname -I | awk '{print $1}')

# 显示结果
echo -e "\n${CYAN}🎉 启动完成！${NC}"
echo -e "${CYAN}============${NC}"
echo -e "${GREEN}🔍 SearXNG: http://localhost:8885${NC}"
echo -e "${GREEN}🤖 MCP Server: http://$WSL_IP:8881${NC}"
echo -e "${GREEN}🏥 健康检查: http://$WSL_IP:8881/health${NC}"

echo -e "\n${BLUE}🐳 Docker LLM配置:${NC}"
echo -e "${GREEN}MCP Server URL: http://host.docker.internal:8881${NC}"

echo -e "\n${YELLOW}📝 日志文件:${NC}"
echo -e "SearXNG: $PROJECT_DIR/logs/searxng.log"
echo -e "MCP Server: $PROJECT_DIR/enhanced_search_mcp/logs/mcp_server.log"

echo -e "\n${YELLOW}🔧 管理命令:${NC}"
echo -e "停止SearXNG: kill $SEARXNG_PID"
echo -e "停止MCP Server: kill $MCP_PID"
echo -e "查看SearXNG日志: tail -f logs/searxng.log"
echo -e "查看MCP日志: tail -f enhanced_search_mcp/logs/mcp_server.log"

echo -e "\n${GREEN}✅ 所有服务已在后台运行${NC}"
