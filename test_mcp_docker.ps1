# 测试MCP Server与Docker LLM连通性的脚本

Write-Host "🧪 测试MCP Server与Docker连通性" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan

# 获取WSL IP
Write-Host "📍 获取WSL IP地址..." -ForegroundColor Blue
$wslIP = (wsl hostname -I).Trim().Split()[0]
Write-Host "WSL IP: $wslIP" -ForegroundColor Green

# 测试1: 直接访问MCP Server
Write-Host "`n🤖 测试1: 直接访问MCP Server" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://$wslIP:8881/health" -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ MCP Server健康检查通过" -ForegroundColor Green
        $healthData = $response.Content | ConvertFrom-Json
        Write-Host "   状态: $($healthData.status)" -ForegroundColor Gray
        Write-Host "   时间: $($healthData.timestamp)" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ MCP Server连接失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请确保MCP Server已启动: .\start_mcp_wsl.sh" -ForegroundColor Yellow
}

# 测试2: 检查MCP工具列表
Write-Host "`n🛠️ 测试2: 检查MCP工具列表" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://$wslIP:8881/tools" -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ MCP工具列表获取成功" -ForegroundColor Green
        $tools = $response.Content | ConvertFrom-Json
        Write-Host "   可用工具数量: $($tools.tools.Count)" -ForegroundColor Gray
        foreach ($tool in $tools.tools | Select-Object -First 3) {
            Write-Host "   - $($tool.name): $($tool.description)" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "❌ 工具列表获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试3: Docker容器访问测试
Write-Host "`n🐳 测试3: Docker容器访问测试" -ForegroundColor Yellow

# 检查Docker是否运行
try {
    $dockerVersion = docker version --format "{{.Server.Version}}" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker运行正常 (版本: $dockerVersion)" -ForegroundColor Green
        
        # 测试host.docker.internal访问
        Write-Host "   测试host.docker.internal访问..." -ForegroundColor Blue
        $dockerTest = docker run --rm --add-host host.docker.internal:host-gateway alpine:latest sh -c "wget -q --spider http://host.docker.internal:8881/health && echo 'SUCCESS' || echo 'FAILED'" 2>$null
        
        if ($dockerTest -eq "SUCCESS") {
            Write-Host "   ✅ Docker可以通过host.docker.internal访问MCP Server" -ForegroundColor Green
        } else {
            Write-Host "   ❌ Docker无法通过host.docker.internal访问MCP Server" -ForegroundColor Red
        }
        
        # 测试直接IP访问
        Write-Host "   测试直接IP访问..." -ForegroundColor Blue
        $dockerIPTest = docker run --rm alpine:latest sh -c "wget -q --spider http://$wslIP:8881/health && echo 'SUCCESS' || echo 'FAILED'" 2>$null
        
        if ($dockerIPTest -eq "SUCCESS") {
            Write-Host "   ✅ Docker可以通过WSL IP访问MCP Server" -ForegroundColor Green
        } else {
            Write-Host "   ❌ Docker无法通过WSL IP访问MCP Server" -ForegroundColor Red
        }
        
    } else {
        Write-Host "❌ Docker未运行" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Docker测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试4: 搜索功能测试
Write-Host "`n🔍 测试4: 搜索功能测试" -ForegroundColor Yellow
try {
    $searchBody = @{
        query = "test search"
        max_results = 3
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "http://$wslIP:8881/search" -Method POST -Body $searchBody -ContentType "application/json" -TimeoutSec 10 -UseBasicParsing -ErrorAction Stop
    
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ 搜索功能正常" -ForegroundColor Green
        $searchResult = $response.Content | ConvertFrom-Json
        Write-Host "   搜索结果数量: $($searchResult.results.Count)" -ForegroundColor Gray
        if ($searchResult.results.Count -gt 0) {
            Write-Host "   第一个结果: $($searchResult.results[0].title)" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "❌ 搜索功能测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 生成Docker Compose配置示例
Write-Host "`n📋 Docker Compose配置示例" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan

$dockerComposeExample = @"
version: '3.8'

services:
  your-llm:
    image: your-llm-image:latest
    container_name: your-llm-container
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - MCP_SERVER_URL=http://host.docker.internal:8881
      - MCP_HEALTH_URL=http://host.docker.internal:8881/health
    ports:
      - "your-llm-port:internal-port"
    restart: unless-stopped
"@

Write-Host $dockerComposeExample -ForegroundColor Gray

# 生成Docker run命令示例
Write-Host "`n🚀 Docker run命令示例" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan

$dockerRunExample = @"
docker run -d \
  --name your-llm-container \
  --add-host host.docker.internal:host-gateway \
  -e MCP_SERVER_URL=http://host.docker.internal:8881 \
  -e MCP_HEALTH_URL=http://host.docker.internal:8881/health \
  -p your-llm-port:internal-port \
  your-llm-image:latest
"@

Write-Host $dockerRunExample -ForegroundColor Gray

# 总结
Write-Host "`n📊 测试总结" -ForegroundColor Cyan
Write-Host "=========" -ForegroundColor Cyan
Write-Host "🎯 MCP Server地址: http://$wslIP:8881" -ForegroundColor Blue
Write-Host "🐳 Docker访问地址: http://host.docker.internal:8881" -ForegroundColor Blue
Write-Host "🏥 健康检查端点: /health" -ForegroundColor Blue
Write-Host "🛠️ 工具列表端点: /tools" -ForegroundColor Blue
Write-Host "🔍 搜索端点: /search (POST)" -ForegroundColor Blue

Write-Host "`n💡 使用提示:" -ForegroundColor Yellow
Write-Host "1. 确保WSL中的MCP Server正在运行" -ForegroundColor Gray
Write-Host "2. 在Docker容器中使用 --add-host host.docker.internal:host-gateway" -ForegroundColor Gray
Write-Host "3. LLM配置MCP Server URL为: http://host.docker.internal:8881" -ForegroundColor Gray
Write-Host "4. 如果连接失败，检查Windows防火墙设置" -ForegroundColor Gray
