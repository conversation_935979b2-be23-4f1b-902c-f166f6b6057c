# 🎉 SearXNG + MCP Server 最终部署指南

## ✅ 部署成功！

你的SearXNG和MCP Server已经成功部署并运行在WSL环境中。

## 🚀 启动服务

### 推荐方式：使用简化启动脚本
```bash
./start_simple.sh
```

这个脚本会：
- ✅ 自动初始化conda环境
- ✅ 激活searxng虚拟环境
- ✅ 安装所需依赖
- ✅ 设置正确的环境变量
- ✅ 启动SearXNG和MCP Server
- ✅ 验证服务状态

## 📊 服务信息

### 当前运行的服务：
- **SearXNG**: `http://localhost:8885` (仅本地访问)
- **MCP Server**: `http://**************:8881` (外部可访问)

### MCP Server端点：
- **SSE端点**: `http://**************:8881/sse`
- **消息端点**: `http://**************:8881/messages/`

## 🐳 Docker LLM配置

### 在Docker容器中配置LLM访问MCP Server：

#### Docker Compose配置：
```yaml
services:
  your-llm:
    image: your-llm-image
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - MCP_SERVER_URL=http://host.docker.internal:8881/sse
```

#### Docker Run命令：
```bash
docker run --add-host host.docker.internal:host-gateway \
  -e MCP_SERVER_URL=http://host.docker.internal:8881/sse \
  your-llm-image
```

## 🔧 管理命令

### 测试服务状态：
```bash
./test_mcp_simple.sh
```

### 查看日志：
```bash
# SearXNG日志
tail -f logs/searxng.log

# MCP Server日志
tail -f enhanced_search_mcp/logs/mcp_server.log
```

### 停止服务：
```bash
# 查找进程ID
ps aux | grep -E "(searx|server.py)"

# 停止服务
kill <PID>
```

## 🌐 网络架构

```
Windows Host
└── WSL2 环境
    ├── SearXNG (127.0.0.1:8885) ← 仅本地访问
    └── MCP Server (0.0.0.0:8881) ← 外部可访问
        └── SSE端点: /sse
        └── 消息端点: /messages/

Docker LLM → host.docker.internal:8881/sse → WSL MCP Server → localhost:8885 SearXNG
```

## 🔍 MCP协议说明

你的MCP Server使用**FastMCP**框架，支持：
- ✅ Server-Sent Events (SSE) 传输协议
- ✅ 实时双向通信
- ✅ 流式响应
- ✅ 兼容Agent-Zero等客户端

### 可用的MCP工具：
1. **enhanced_search** - 增强搜索（支持多轮搜索策略）
2. **basic_search** - 基础搜索
3. **get_search_stats** - 获取搜索统计
4. **get_system_info** - 获取系统信息

## 📝 重要配置

### 环境变量（已自动设置）：
```bash
SEARXNG_PORT=8885
SEARXNG_BIND_ADDRESS=127.0.0.1  # 仅本地访问
SEARXNG_SECRET=<随机生成>        # 安全密钥
MCP_HOST=0.0.0.0                # 监听所有接口
MCP_PORT=8881
SEARXNG_URL=http://localhost:8885
```

### Conda环境：
- **环境名称**: searxng
- **Python版本**: 3.11.13
- **环境路径**: `/home/<USER>/miniconda/envs/searxng`

## 🧪 验证部署

运行测试脚本验证所有功能：
```bash
./test_mcp_simple.sh
```

预期结果：
- ✅ 端口8881可连接
- ✅ MCP服务器进程运行中
- ✅ SearXNG运行正常

## 🔄 重启服务

如果需要重启服务：
```bash
# 停止旧进程
pkill -f "python.*searx.webapp"
pkill -f "python.*server.py"

# 重新启动
./start_simple.sh
```

## 💡 使用提示

1. **服务持久化**: 服务在WSL中后台运行，关闭终端不会停止服务
2. **日志监控**: 定期检查日志文件确保服务正常
3. **性能优化**: MCP Server支持并发搜索，默认最大5个并发
4. **安全考虑**: SearXNG只监听本地，MCP Server通过环境变量配置访问控制

## 🎯 下一步

1. **配置你的LLM**: 使用上述Docker配置连接MCP Server
2. **测试搜索功能**: 通过LLM客户端测试搜索工具
3. **监控性能**: 定期运行测试脚本检查服务状态
4. **自定义配置**: 根据需要调整搜索引擎和参数

## 🆘 故障排除

如果遇到问题：
1. 运行 `./test_mcp_simple.sh` 检查服务状态
2. 查看日志文件定位问题
3. 确认conda环境正确激活
4. 检查端口是否被占用
5. 验证网络连接

---

🎉 **恭喜！你的SearXNG + MCP Server已成功部署并可以为Docker中的LLM提供搜索服务！**
