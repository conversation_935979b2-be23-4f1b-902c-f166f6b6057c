#!/bin/bash

# WSL网络配置脚本 - 为Docker访问配置网络
# 确保WSL中的服务可以被Windows Docker容器访问

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}🌐 WSL网络配置脚本${NC}"
echo -e "${CYAN}==================${NC}"

# 获取WSL IP地址
echo -e "${BLUE}📍 获取WSL网络信息...${NC}"
WSL_IPS=$(hostname -I)
echo -e "${GREEN}WSL IP地址: $WSL_IPS${NC}"

# 获取主要IP地址（第一个）
PRIMARY_IP=$(echo $WSL_IPS | awk '{print $1}')
echo -e "${GREEN}主要IP地址: $PRIMARY_IP${NC}"

# 检查防火墙状态
echo -e "${BLUE}🔥 检查防火墙状态...${NC}"
if command -v ufw &> /dev/null; then
    UFW_STATUS=$(sudo ufw status | head -1)
    echo -e "${YELLOW}UFW状态: $UFW_STATUS${NC}"
    
    if [[ "$UFW_STATUS" == *"active"* ]]; then
        echo -e "${YELLOW}配置UFW防火墙规则...${NC}"
        sudo ufw allow 8885/tcp comment "SearXNG Web Interface"
        sudo ufw allow 8881/tcp comment "MCP Server"
        echo -e "${GREEN}✅ 防火墙规则已添加${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ UFW未安装，跳过防火墙配置${NC}"
fi

# 检查端口监听状态
echo -e "${BLUE}🔌 检查端口监听状态...${NC}"
check_port_listening() {
    local port=$1
    local service=$2
    if netstat -ln | grep -q ":$port "; then
        echo -e "${GREEN}✅ 端口 $port ($service) 正在监听${NC}"
        # 显示监听地址
        netstat -ln | grep ":$port " | while read line; do
            echo -e "${BLUE}   $line${NC}"
        done
    else
        echo -e "${YELLOW}⚠️ 端口 $port ($service) 未在监听${NC}"
    fi
}

check_port_listening 8885 "SearXNG"
check_port_listening 8881 "MCP Server"

# 测试网络连通性
echo -e "${BLUE}🔍 测试网络连通性...${NC}"

# 测试本地连接
test_local_connection() {
    local port=$1
    local service=$2
    local url="http://localhost:$port"
    
    if curl -s --connect-timeout 5 "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $service 本地连接正常 ($url)${NC}"
    else
        echo -e "${YELLOW}⚠️ $service 本地连接失败 ($url)${NC}"
    fi
}

# 测试外部IP连接
test_external_connection() {
    local port=$1
    local service=$2
    local url="http://$PRIMARY_IP:$port"
    
    if curl -s --connect-timeout 5 "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $service 外部连接正常 ($url)${NC}"
    else
        echo -e "${YELLOW}⚠️ $service 外部连接失败 ($url)${NC}"
    fi
}

test_local_connection 8885 "SearXNG"
test_local_connection 8881 "MCP Server"
test_external_connection 8885 "SearXNG"
test_external_connection 8881 "MCP Server"

# 生成Docker配置建议
echo -e "\n${CYAN}🐳 Docker配置建议${NC}"
echo -e "${CYAN}=================${NC}"

echo -e "${YELLOW}方法1: 使用host.docker.internal (推荐)${NC}"
echo -e "${BLUE}在Docker容器中使用以下地址:${NC}"
echo -e "${GREEN}  SearXNG: http://host.docker.internal:8885${NC}"
echo -e "${GREEN}  MCP Server: http://host.docker.internal:8881${NC}"
echo -e "${GRAY}  Docker运行命令: docker run --add-host host.docker.internal:host-gateway ...${NC}"

echo -e "\n${YELLOW}方法2: 直接使用WSL IP地址${NC}"
echo -e "${BLUE}在Docker容器中使用以下地址:${NC}"
echo -e "${GREEN}  SearXNG: http://$PRIMARY_IP:8885${NC}"
echo -e "${GREEN}  MCP Server: http://$PRIMARY_IP:8881${NC}"

echo -e "\n${YELLOW}方法3: 使用Docker host网络模式${NC}"
echo -e "${BLUE}Docker运行命令:${NC}"
echo -e "${GREEN}  docker run --network host ...${NC}"
echo -e "${BLUE}容器内访问地址:${NC}"
echo -e "${GREEN}  SearXNG: http://localhost:8885${NC}"
echo -e "${GREEN}  MCP Server: http://localhost:8881${NC}"

# 生成测试脚本
echo -e "\n${CYAN}🧪 生成测试脚本${NC}"
echo -e "${CYAN}=============${NC}"

cat > test_wsl_services.sh << 'EOF'
#!/bin/bash
# WSL服务测试脚本

echo "🧪 测试WSL服务连通性"
echo "==================="

# 获取WSL IP
WSL_IP=$(hostname -I | awk '{print $1}')
echo "WSL IP: $WSL_IP"

# 测试SearXNG
echo -n "测试SearXNG (localhost): "
if curl -s --connect-timeout 5 http://localhost:8885 > /dev/null; then
    echo "✅ 成功"
else
    echo "❌ 失败"
fi

echo -n "测试SearXNG (WSL IP): "
if curl -s --connect-timeout 5 http://$WSL_IP:8885 > /dev/null; then
    echo "✅ 成功"
else
    echo "❌ 失败"
fi

# 测试MCP Server
echo -n "测试MCP Server (localhost): "
if curl -s --connect-timeout 5 http://localhost:8881/health > /dev/null; then
    echo "✅ 成功"
else
    echo "❌ 失败"
fi

echo -n "测试MCP Server (WSL IP): "
if curl -s --connect-timeout 5 http://$WSL_IP:8881/health > /dev/null; then
    echo "✅ 成功"
else
    echo "❌ 失败"
fi

# 测试从Docker容器访问
echo -n "测试Docker容器访问 (需要Docker运行): "
if command -v docker &> /dev/null; then
    if docker run --rm --add-host host.docker.internal:host-gateway alpine:latest wget -q --spider http://host.docker.internal:8885 2>/dev/null; then
        echo "✅ 成功"
    else
        echo "❌ 失败"
    fi
else
    echo "⚠️ Docker未安装"
fi
EOF

chmod +x test_wsl_services.sh
echo -e "${GREEN}✅ 测试脚本已生成: test_wsl_services.sh${NC}"

echo -e "\n${CYAN}📋 总结${NC}"
echo -e "${CYAN}=====${NC}"
echo -e "${GREEN}✅ WSL网络配置完成${NC}"
echo -e "${BLUE}📍 WSL主要IP地址: $PRIMARY_IP${NC}"
echo -e "${BLUE}🔧 运行测试: ./test_wsl_services.sh${NC}"
echo -e "${BLUE}🚀 启动服务: ./scripts/start_searxng_with_mcp.sh${NC}"

echo -e "\n${YELLOW}💡 下一步操作:${NC}"
echo -e "${GRAY}1. 启动WSL中的SearXNG+MCP服务${NC}"
echo -e "${GRAY}2. 在Windows Docker中配置LLM访问 host.docker.internal:8881${NC}"
echo -e "${GRAY}3. 运行测试脚本验证连通性${NC}"
