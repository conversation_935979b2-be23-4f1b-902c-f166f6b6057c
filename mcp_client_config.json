{"name": "enhanced-search", "type": "sse", "url": "http://host.docker.internal:8881/sse", "description": "增强搜索引擎MCP服务器", "version": "1.0.0", "protocol": "mcp", "transport": "server-sent-events", "capabilities": {"tools": [{"name": "enhanced_search", "description": "执行增强搜索，支持多轮搜索策略", "parameters": {"query": {"type": "string", "required": true, "description": "搜索查询内容"}, "depth": {"type": "string", "default": "deep", "enum": ["basic", "deep", "comprehensive"], "description": "搜索深度"}, "max_results": {"type": "integer", "default": 20, "minimum": 1, "maximum": 50, "description": "最大结果数量"}}}, {"name": "basic_search", "description": "执行基础搜索", "parameters": {"query": {"type": "string", "required": true, "description": "搜索查询内容"}, "engines": {"type": "string", "default": "", "description": "搜索引擎列表（逗号分隔）"}, "max_results": {"type": "integer", "default": 10, "minimum": 1, "maximum": 30, "description": "最大结果数量"}}}, {"name": "get_search_stats", "description": "获取搜索统计信息", "parameters": {}}, {"name": "get_system_info", "description": "获取系统信息", "parameters": {}}]}, "connection": {"timeout": 30, "retry_attempts": 3, "retry_delay": 1000, "keep_alive": true}, "docker_requirements": {"extra_hosts": ["host.docker.internal:host-gateway"], "environment": {"MCP_SERVER_URL": "http://host.docker.internal:8881/sse"}}, "health_check": {"endpoint": "http://host.docker.internal:8881/sse", "method": "GET", "expected_response": "text/plain", "timeout": 5}, "fallback_urls": ["http://**************:8881/sse"], "metadata": {"author": "SearXNG + MCP Integration", "created": "2024-01-01", "last_updated": "2024-01-01", "status": "active"}}