#!/bin/bash

# 简单的MCP服务器测试脚本

echo "🧪 MCP服务器简单测试"
echo "==================="

WSL_IP=$(hostname -I | awk '{print $1}')
echo "WSL IP: $WSL_IP"

# 测试端口连接
echo -e "\n1. 测试端口连接:"
if timeout 5 bash -c "</dev/tcp/localhost/8881"; then
    echo "✅ 端口8881可连接"
else
    echo "❌ 端口8881不可连接"
    exit 1
fi

# 测试SSE端点
echo -e "\n2. 测试SSE端点:"
response=$(timeout 3 curl -s "http://localhost:8881/sse" | head -1)
if [ -n "$response" ]; then
    echo "✅ SSE端点响应: $response"
else
    echo "⚠️ SSE端点无响应（这可能是正常的，因为SSE是流式连接）"
fi

# 测试消息端点
echo -e "\n3. 测试消息端点:"
response=$(curl -s -w "%{http_code}" -o /dev/null "http://localhost:8881/messages/")
echo "HTTP状态码: $response"

# 检查进程
echo -e "\n4. 检查进程状态:"
if pgrep -f "python.*server.py" > /dev/null; then
    pid=$(pgrep -f "python.*server.py")
    echo "✅ MCP服务器进程运行中 (PID: $pid)"
else
    echo "❌ MCP服务器进程未运行"
fi

# 检查SearXNG
echo -e "\n5. 检查SearXNG状态:"
if curl -s --connect-timeout 3 "http://localhost:8885" > /dev/null; then
    echo "✅ SearXNG运行正常"
else
    echo "❌ SearXNG连接失败"
fi

# 显示服务信息
echo -e "\n📋 服务信息:"
echo "🔍 SearXNG: http://localhost:8885"
echo "🤖 MCP Server: http://$WSL_IP:8881"
echo "📡 MCP SSE端点: http://$WSL_IP:8881/sse"
echo "📨 MCP消息端点: http://$WSL_IP:8881/messages/"

echo -e "\n🐳 Docker LLM配置:"
echo "MCP Server URL: http://host.docker.internal:8881/sse"
echo "Docker参数: --add-host host.docker.internal:host-gateway"

echo -e "\n✅ 测试完成"
