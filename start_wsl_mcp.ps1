# WSL中启动SearXNG + MCP Server的PowerShell脚本
param(
    [switch]$Stop,
    [switch]$Status,
    [switch]$Logs,
    [switch]$Test,
    [switch]$Help
)

function Show-Help {
    Write-Host "🤖 WSL MCP Server启动脚本" -ForegroundColor Cyan
    Write-Host "========================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "用法:" -ForegroundColor Yellow
    Write-Host "  .\start_wsl_mcp.ps1 [选项]" -ForegroundColor White
    Write-Host ""
    Write-Host "选项:" -ForegroundColor Yellow
    Write-Host "  (无参数)    启动SearXNG + MCP服务器" -ForegroundColor White
    Write-Host "  -Stop       停止所有服务" -ForegroundColor White
    Write-Host "  -Status     查看服务状态" -ForegroundColor White
    Write-Host "  -Logs       查看服务日志" -ForegroundColor White
    Write-Host "  -Test       测试服务连通性" -ForegroundColor White
    Write-Host "  -Help       显示此帮助信息" -ForegroundColor White
}

if ($Help) {
    Show-Help
    exit 0
}

Write-Host "🤖 WSL MCP Server管理脚本" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan

# 检查WSL是否可用
Write-Host "🔍 检查WSL环境..." -ForegroundColor Blue
try {
    $wslVersion = wsl --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ WSL运行正常" -ForegroundColor Green
    } else {
        throw "WSL命令执行失败"
    }
} catch {
    Write-Host "❌ WSL未安装或未运行" -ForegroundColor Red
    Write-Host "请确保WSL已安装并配置" -ForegroundColor Yellow
    exit 1
}

# 获取WSL IP地址
Write-Host "📍 获取WSL IP地址..." -ForegroundColor Blue
$wslIP = (wsl hostname -I).Trim().Split()[0]
Write-Host "WSL IP地址: $wslIP" -ForegroundColor Green

# 停止服务
if ($Stop) {
    Write-Host "🛑 停止WSL中的服务..." -ForegroundColor Yellow
    wsl -e bash -c "pkill -f 'python.*searx.webapp' || true"
    wsl -e bash -c "pkill -f 'python.*server.py' || true"
    wsl -e bash -c "pkill -f 'python.*enhanced_search_mcp' || true"
    Start-Sleep -Seconds 2
    Write-Host "✅ 服务停止命令已发送" -ForegroundColor Green
    exit 0
}

# 查看服务状态
if ($Status) {
    Write-Host "📊 检查服务状态..." -ForegroundColor Blue
    
    Write-Host "`n🔍 SearXNG (端口8885):" -ForegroundColor Yellow
    $searxngProcess = wsl -e bash -c "pgrep -f 'python.*searx.webapp' || echo 'not_running'"
    if ($searxngProcess -ne "not_running") {
        Write-Host "✅ SearXNG正在运行 (PID: $searxngProcess)" -ForegroundColor Green
    } else {
        Write-Host "❌ SearXNG未运行" -ForegroundColor Red
    }
    
    Write-Host "`n🤖 MCP Server (端口8881):" -ForegroundColor Yellow
    $mcpProcess = wsl -e bash -c "pgrep -f 'python.*server.py' || echo 'not_running'"
    if ($mcpProcess -ne "not_running") {
        Write-Host "✅ MCP Server正在运行 (PID: $mcpProcess)" -ForegroundColor Green
    } else {
        Write-Host "❌ MCP Server未运行" -ForegroundColor Red
    }
    
    Write-Host "`n🔌 端口监听状态:" -ForegroundColor Yellow
    wsl -e bash -c "netstat -ln | grep ':888[15] ' || echo '未找到监听端口'"
    
    exit 0
}

# 查看日志
if ($Logs) {
    Write-Host "📋 查看服务日志..." -ForegroundColor Blue
    Write-Host "按 Ctrl+C 退出日志查看" -ForegroundColor Yellow
    Write-Host ""
    
    # 显示最近的日志
    Write-Host "=== SearXNG日志 ===" -ForegroundColor Cyan
    wsl -e bash -c "tail -20 logs/searxng.log 2>/dev/null || echo '日志文件不存在'"
    
    Write-Host "`n=== MCP Server日志 ===" -ForegroundColor Cyan
    wsl -e bash -c "tail -20 enhanced_search_mcp/logs/mcp_server.log 2>/dev/null || echo '日志文件不存在'"
    
    exit 0
}

# 测试服务连通性
if ($Test) {
    Write-Host "🧪 测试服务连通性..." -ForegroundColor Blue
    
    # 测试MCP Server
    Write-Host "`n🤖 测试MCP Server:" -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri "http://$wslIP:8881/health" -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ MCP Server健康检查通过" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ MCP Server连接失败" -ForegroundColor Red
    }
    
    # 测试SearXNG
    Write-Host "`n🔍 测试SearXNG:" -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri "http://$wslIP:8885" -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ SearXNG连接正常" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ SearXNG连接失败" -ForegroundColor Red
    }
    
    # 测试Docker访问
    Write-Host "`n🐳 测试Docker访问:" -ForegroundColor Yellow
    try {
        $dockerTest = docker run --rm --add-host host.docker.internal:host-gateway alpine:latest wget -q --spider http://host.docker.internal:8881/health 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Docker可以访问MCP Server" -ForegroundColor Green
        } else {
            Write-Host "❌ Docker无法访问MCP Server" -ForegroundColor Red
        }
    } catch {
        Write-Host "⚠️ Docker测试失败（可能Docker未运行）" -ForegroundColor Yellow
    }
    
    exit 0
}

# 启动服务
Write-Host "🚀 启动WSL中的SearXNG + MCP服务器..." -ForegroundColor Blue

# 检查conda环境
Write-Host "🐍 检查conda环境..." -ForegroundColor Blue
$condaCheck = wsl -e bash -c "source ~/miniconda3/etc/profile.d/conda.sh 2>/dev/null || source ~/anaconda3/etc/profile.d/conda.sh 2>/dev/null; conda env list | grep searxng || echo 'not_found'"

if ($condaCheck -eq "not_found") {
    Write-Host "❌ searxng conda环境不存在" -ForegroundColor Red
    Write-Host "请先创建conda环境: conda create -n searxng python=3.11" -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "✅ searxng conda环境存在" -ForegroundColor Green
}

# 停止可能运行的旧进程
Write-Host "🛑 清理旧进程..." -ForegroundColor Yellow
wsl -e bash -c "pkill -f 'python.*searx.webapp' || true"
wsl -e bash -c "pkill -f 'python.*server.py' || true"
Start-Sleep -Seconds 2

# 创建启动脚本
Write-Host "📝 创建WSL启动脚本..." -ForegroundColor Blue
$startScript = @"
#!/bin/bash
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "`${CYAN}🚀 启动SearXNG + MCP服务器`${NC}"

# 初始化conda
source ~/miniconda3/etc/profile.d/conda.sh 2>/dev/null || source ~/anaconda3/etc/profile.d/conda.sh

# 激活searxng环境
echo -e "`${YELLOW}🔄 激活searxng conda环境...`${NC}"
conda activate searxng

# 设置环境变量
export SEARXNG_PORT="8885"
export SEARXNG_BIND_ADDRESS="127.0.0.1"
export SEARXNG_URL="http://localhost:8885"
export MCP_HOST="0.0.0.0"
export MCP_PORT="8881"
export LOG_LEVEL="INFO"

# 创建日志目录
mkdir -p logs enhanced_search_mcp/logs

# 启动SearXNG
echo -e "`${PURPLE}🔍 启动SearXNG...`${NC}"
nohup python -m searx.webapp > logs/searxng.log 2>&1 &
SEARXNG_PID=`$!
echo -e "`${GREEN}✅ SearXNG已启动 (PID: `$SEARXNG_PID)`${NC}"

# 等待SearXNG启动
echo -e "`${YELLOW}⏳ 等待SearXNG启动...`${NC}"
for i in {1..30}; do
    if curl -s --connect-timeout 2 http://localhost:8885 > /dev/null; then
        echo -e "`${GREEN}✅ SearXNG启动成功`${NC}"
        break
    fi
    sleep 1
done

# 启动MCP Server
echo -e "`${PURPLE}🤖 启动MCP服务器...`${NC}"
cd enhanced_search_mcp
nohup python server.py > logs/mcp_server.log 2>&1 &
MCP_PID=`$!
echo -e "`${GREEN}✅ MCP服务器已启动 (PID: `$MCP_PID)`${NC}"

# 等待MCP Server启动
echo -e "`${YELLOW}⏳ 等待MCP服务器启动...`${NC}"
for i in {1..20}; do
    if curl -s --connect-timeout 2 http://localhost:8881/health > /dev/null; then
        echo -e "`${GREEN}✅ MCP服务器启动成功`${NC}"
        break
    fi
    sleep 1
done

echo -e "`${CYAN}🎉 所有服务启动完成！`${NC}"
echo -e "`${GREEN}📡 SearXNG: http://localhost:8885`${NC}"
echo -e "`${GREEN}🤖 MCP Server: http://localhost:8881`${NC}"
echo -e "`${GREEN}🏥 健康检查: http://localhost:8881/health`${NC}"
"@

# 将启动脚本写入WSL
$startScript | wsl -e bash -c "cat > start_services.sh && chmod +x start_services.sh"

# 执行启动脚本
Write-Host "🚀 执行启动脚本..." -ForegroundColor Blue
wsl -e bash -c "./start_services.sh"

# 等待服务启动
Write-Host "⏳ 等待服务完全启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# 验证服务状态
Write-Host "`n📊 验证服务状态:" -ForegroundColor Cyan

# 检查MCP Server
try {
    $response = Invoke-WebRequest -Uri "http://$wslIP:8881/health" -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ MCP Server运行正常 (http://$wslIP:8881)" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ MCP Server可能仍在启动中" -ForegroundColor Yellow
}

# 检查SearXNG
try {
    $response = Invoke-WebRequest -Uri "http://$wslIP:8885" -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ SearXNG运行正常 (http://$wslIP:8885)" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ SearXNG可能仍在启动中" -ForegroundColor Yellow
}

Write-Host "`n🎉 启动完成！" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Cyan
Write-Host "🤖 MCP Server地址: http://$wslIP:8881" -ForegroundColor Blue
Write-Host "🐳 Docker LLM配置: http://host.docker.internal:8881" -ForegroundColor Blue
Write-Host "🏥 健康检查: http://$wslIP:8881/health" -ForegroundColor Blue

Write-Host "`n📋 管理命令:" -ForegroundColor Yellow
Write-Host "  查看状态: .\start_wsl_mcp.ps1 -Status" -ForegroundColor Gray
Write-Host "  查看日志: .\start_wsl_mcp.ps1 -Logs" -ForegroundColor Gray
Write-Host "  测试连接: .\start_wsl_mcp.ps1 -Test" -ForegroundColor Gray
Write-Host "  停止服务: .\start_wsl_mcp.ps1 -Stop" -ForegroundColor Gray

Write-Host "`n💡 Docker LLM配置提示:" -ForegroundColor Yellow
Write-Host "在Docker容器中使用: --add-host host.docker.internal:host-gateway" -ForegroundColor Gray
Write-Host "MCP Server URL: http://host.docker.internal:8881" -ForegroundColor Gray
