#!/bin/bash

# MCP Server性能优化脚本

echo "🚀 MCP Server性能优化"
echo "==================="

# 设置优化的环境变量
export SEARXNG_TIMEOUT=15          # 减少超时时间到15秒
export SEARXNG_MAX_RETRIES=2       # 减少重试次数
export CACHE_TTL=1800              # 缓存30分钟
export LOG_LEVEL=INFO              # 减少调试日志

echo "✅ 性能优化环境变量已设置:"
echo "   SEARXNG_TIMEOUT=$SEARXNG_TIMEOUT"
echo "   SEARXNG_MAX_RETRIES=$SEARXNG_MAX_RETRIES" 
echo "   CACHE_TTL=$CACHE_TTL"

# 重启MCP Server
echo -e "\n🔄 重启MCP Server..."
pkill -f "python.*server.py" || true
sleep 2

cd enhanced_search_mcp
source ~/miniconda3/etc/profile.d/conda.sh
conda activate /home/<USER>/miniconda/envs/searxng

nohup python server.py > logs/mcp_server.log 2>&1 &
echo "✅ MCP Server已重启 (PID: $!)"

echo -e "\n💡 使用建议:"
echo "1. 使用 basic_search 获得更快响应"
echo "2. 设置 max_results 为较小值 (5-10)"
echo "3. 对于快速查询，使用 depth='basic'"
