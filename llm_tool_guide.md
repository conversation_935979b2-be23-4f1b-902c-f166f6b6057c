# 🔍 Enhanced Search MCP工具使用指南

## 📋 工具概述

Enhanced Search是一个强大的搜索工具，专为LLM设计，提供高质量的网络搜索和信息收集能力。

## 🛠️ 可用工具

### 1. **enhanced_search** - 主要搜索工具 ⭐

**何时使用**：
- 需要获取最新信息时
- 研究特定主题时
- 需要多角度信息时
- 寻找权威资料时

**参数说明**：
```json
{
  "query": "搜索内容",           // 必需：你想搜索的内容
  "search_depth": "deep",       // 可选：搜索深度
  "max_results": 20,            // 可选：最大结果数
  "enable_cache": true          // 可选：是否使用缓存
}
```

**搜索深度选择**：
- `"basic"` - 快速搜索，2-5秒响应，适合简单查询
- `"deep"` - 深度搜索，5-10秒响应，默认推荐
- `"comprehensive"` - 全面搜索，10-15秒响应，适合复杂研究

**使用示例**：
```json
// 快速查询
{"query": "Python 3.12新特性", "search_depth": "basic", "max_results": 10}

// 深度研究
{"query": "人工智能在医疗诊断中的应用", "search_depth": "comprehensive", "max_results": 30}

// 技术问题
{"query": "Docker容器化最佳实践", "search_depth": "deep", "max_results": 15}
```

### 2. **search_suggestions** - 搜索建议工具

**何时使用**：
- 用户查询不够具体时
- 需要扩展搜索思路时
- 探索相关主题时

**参数**：
```json
{"query": "原始查询内容"}
```

### 3. **search_stats** - 统计信息工具

**何时使用**：
- 检查搜索服务状态
- 了解搜索性能
- 调试搜索问题

### 4. **system_info** - 系统信息工具

**何时使用**：
- 检查服务健康状态
- 了解系统配置
- 技术支持需求

## 🎯 使用场景推荐

### **场景1：用户询问最新信息**
```
用户："最新的AI发展趋势是什么？"
推荐：enhanced_search
参数：{"query": "2024年人工智能发展趋势最新动态", "search_depth": "deep", "max_results": 20}
```

### **场景2：技术问题解答**
```
用户："如何优化React应用性能？"
推荐：enhanced_search
参数：{"query": "React应用性能优化最佳实践", "search_depth": "comprehensive", "max_results": 25}
```

### **场景3：快速事实查询**
```
用户："今天的天气怎么样？"
推荐：enhanced_search
参数：{"query": "今日天气预报", "search_depth": "basic", "max_results": 5}
```

### **场景4：学术研究**
```
用户："量子计算的最新突破有哪些？"
推荐：enhanced_search
参数：{"query": "量子计算最新突破研究进展2024", "search_depth": "comprehensive", "max_results": 30}
```

### **场景5：产品比较**
```
用户："哪款笔记本电脑性价比最高？"
推荐：enhanced_search
参数：{"query": "2024年性价比最高笔记本电脑推荐评测", "search_depth": "deep", "max_results": 20}
```

## 💡 最佳实践

### **查询优化技巧**：
1. **使用具体关键词**：避免过于宽泛的查询
2. **包含时间信息**：如"2024年"、"最新"、"近期"
3. **指定领域**：如"技术"、"医疗"、"教育"
4. **使用专业术语**：提高结果相关性

### **参数选择建议**：
- **快速响应需求**：`search_depth: "basic"`, `max_results: 5-10`
- **平衡质量和速度**：`search_depth: "deep"`, `max_results: 15-20`
- **深度研究需求**：`search_depth: "comprehensive"`, `max_results: 25-30`

### **结果处理建议**：
1. **总结关键信息**：提取最重要的发现
2. **引用来源**：提及权威网站和资料
3. **多角度分析**：整合不同观点
4. **时效性说明**：注明信息的时间范围

## 🚨 注意事项

### **使用限制**：
- 查询不能为空
- 最大结果数：1-50
- 搜索深度：basic/deep/comprehensive

### **性能考虑**：
- basic搜索：最快，适合简单查询
- deep搜索：平衡，推荐日常使用
- comprehensive搜索：最慢但最全面

### **错误处理**：
- 如果搜索失败，会返回错误信息
- 可以尝试简化查询或调整参数
- 检查网络连接和服务状态

## 🔄 工作流程建议

1. **分析用户需求** → 确定搜索类型
2. **选择合适工具** → enhanced_search为主
3. **优化查询参数** → 根据需求调整深度和结果数
4. **执行搜索** → 调用工具获取结果
5. **处理和总结** → 整理信息并回答用户
6. **必要时补充** → 使用search_suggestions获取更多角度

## 📈 效果评估

**好的搜索结果特征**：
- 信息新鲜且相关
- 来源权威可信
- 内容丰富详细
- 多角度覆盖主题

**需要改进的信号**：
- 结果过时或不相关
- 信息质量较低
- 缺少权威来源
- 覆盖面不够全面

通过合理使用这些工具，你可以为用户提供高质量、及时、全面的信息服务！
