#!/usr/bin/env python3
"""
Enhanced Search MCP Server - 直接模块调用版本
使用SearXNGDirectAdapter直接调用SearXNG Python模块，无需HTTP API
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from fastmcp import FastMCP
from config import load_config
from enhanced_search_engine import EnhancedSearchEngine

# 尝试导入直接适配器
try:
    from searxng_direct_adapter import SearXNGDirectAdapter
    DIRECT_ADAPTER_AVAILABLE = True
except ImportError as e:
    DIRECT_ADAPTER_AVAILABLE = False
    IMPORT_ERROR = str(e)

# 备用适配器
from fallback_search_adapter import FallbackSearchAdapter

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 全局变量
mcp = FastMCP("Enhanced Search Engine")
search_engine = None
config = None

async def initialize_components():
    """初始化组件"""
    global search_engine, config
    
    try:
        # 加载配置
        config = load_config()
        logger.info("配置加载成功")
        
        # 优先尝试使用直接适配器
        if DIRECT_ADAPTER_AVAILABLE:
            try:
                logger.info("尝试使用SearXNG直接适配器...")
                searxng_adapter = SearXNGDirectAdapter(
                    timeout=config.SEARXNG_TIMEOUT,
                    max_retries=config.SEARXNG_MAX_RETRIES
                )
                logger.info("SearXNG直接适配器创建成功")
                
                # 初始化适配器
                await searxng_adapter.initialize()
                logger.info("SearXNG直接适配器初始化完成")
                
                # 测试SearXNG连接
                health_status = await searxng_adapter.health_check()
                if health_status['status'] == 'healthy':
                    logger.info("SearXNG直接模块测试成功")
                    # 创建搜索引擎
                    search_engine = EnhancedSearchEngine(searxng_adapter, config)
                    logger.info("增强搜索引擎创建成功（使用直接模块）")
                    return
                else:
                    logger.warning(f"SearXNG直接模块异常: {health_status}")
                    raise Exception("SearXNG直接模块不可用")
                    
            except Exception as e:
                logger.warning(f"SearXNG直接适配器初始化失败: {e}")
                logger.info("切换到备用搜索适配器...")
        else:
            logger.warning(f"SearXNG直接适配器不可用: {IMPORT_ERROR}")
            logger.info("使用备用搜索适配器...")
        
        # 创建备用搜索适配器
        fallback_adapter = FallbackSearchAdapter(
            timeout=config.SEARXNG_TIMEOUT,
            max_retries=config.SEARXNG_MAX_RETRIES
        )
        logger.info("备用搜索适配器创建成功")
        
        # 初始化备用适配器
        await fallback_adapter.initialize()
        logger.info("备用搜索适配器初始化完成")
        
        # 测试备用适配器连接
        health_status = await fallback_adapter.health_check()
        if health_status['status'] == 'healthy':
            logger.info("备用搜索适配器连接测试成功")
            # 创建搜索引擎
            search_engine = EnhancedSearchEngine(fallback_adapter, config)
            logger.info("增强搜索引擎创建成功（使用备用适配器）")
        else:
            logger.error(f"备用搜索适配器也不可用: {health_status}")
            raise Exception("所有搜索适配器都不可用")
            
    except Exception as e:
        logger.error(f"组件初始化失败: {e}")
        raise

@mcp.tool()
async def enhanced_search(query: str, depth: str = "deep", max_results: int = 20) -> str:
    """
    执行增强搜索
    
    Args:
        query: 搜索查询内容
        depth: 搜索深度 ("basic", "deep", "comprehensive")
        max_results: 最大结果数量
    
    Returns:
        格式化的搜索结果和摘要
    """
    try:
        if not search_engine:
            return "❌ 搜索引擎未初始化"
        
        logger.info(f"执行增强搜索: '{query}' (深度: {depth})")
        result = await search_engine.execute(query, depth, max_results)
        return result
        
    except Exception as e:
        logger.error(f"搜索执行失败: {e}")
        return f"❌ 搜索失败: {str(e)}"

@mcp.tool()
async def basic_search(query: str, engines: str = "", max_results: int = 10) -> str:
    """
    执行基础搜索
    
    Args:
        query: 搜索查询内容
        engines: 搜索引擎列表（逗号分隔）
        max_results: 最大结果数量
    
    Returns:
        搜索结果JSON字符串
    """
    try:
        if not search_engine:
            return "❌ 搜索引擎未初始化"
        
        # 解析引擎列表
        engine_list = [e.strip() for e in engines.split(",")] if engines else None
        
        logger.info(f"执行基础搜索: '{query}' (引擎: {engine_list})")
        results = await search_engine._basic_search(query, engine_list)
        
        # 限制结果数量
        limited_results = results[:max_results]
        
        import json
        return json.dumps({
            "query": query,
            "engines": engine_list or search_engine.searxng.default_engines,
            "results_count": len(limited_results),
            "results": limited_results
        }, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"基础搜索失败: {e}")
        return f"❌ 基础搜索失败: {str(e)}"

@mcp.tool()
async def get_search_stats() -> str:
    """
    获取搜索统计信息
    
    Returns:
        统计信息JSON字符串
    """
    try:
        if not search_engine:
            return "❌ 搜索引擎未初始化"
        
        # 获取搜索引擎统计
        engine_stats = search_engine.get_stats()
        
        # 获取适配器统计
        adapter_stats = search_engine.searxng.get_stats()
        
        import json
        stats = {
            "search_engine": engine_stats,
            "adapter": adapter_stats,
            "adapter_type": type(search_engine.searxng).__name__
        }
        
        return json.dumps(stats, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return f"❌ 获取统计信息失败: {str(e)}"

async def health_check():
    """健康检查端点"""
    try:
        if not search_engine:
            return {
                "status": "unhealthy",
                "error": "搜索引擎未初始化",
                "timestamp": "2024-01-01T00:00:00"
            }
        
        # 检查适配器健康状态
        adapter_health = await search_engine.searxng.health_check()
        
        return {
            "status": "healthy" if adapter_health["status"] == "healthy" else "unhealthy",
            "adapter_type": type(search_engine.searxng).__name__,
            "adapter_health": adapter_health,
            "timestamp": adapter_health.get("timestamp", "unknown")
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": "2024-01-01T00:00:00"
        }

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"接收到信号 {signum}，正在关闭服务器...")
    sys.exit(0)

async def main():
    """主函数"""
    try:
        # 设置信号处理
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        logger.info("🚀 启动Enhanced Search MCP服务器（直接模块版本）")
        
        # 初始化组件
        await initialize_components()
        
        # 启动MCP服务器
        logger.info(f"🤖 MCP服务器启动在 {config.HOST}:{config.PORT}")
        logger.info(f"🔍 使用适配器: {type(search_engine.searxng).__name__}")
        
        # 添加健康检查路由
        from fastapi import FastAPI
        app = FastAPI()
        
        @app.get("/health")
        async def health():
            return await health_check()
        
        @app.get("/tools")
        async def tools():
            return {
                "tools": [
                    {
                        "name": "enhanced_search",
                        "description": "执行增强搜索，支持多轮搜索策略",
                        "parameters": ["query", "depth", "max_results"]
                    },
                    {
                        "name": "basic_search", 
                        "description": "执行基础搜索",
                        "parameters": ["query", "engines", "max_results"]
                    },
                    {
                        "name": "get_search_stats",
                        "description": "获取搜索统计信息",
                        "parameters": []
                    }
                ]
            }
        
        # 运行服务器
        import uvicorn
        await uvicorn.run(
            app,
            host=config.HOST,
            port=config.PORT,
            log_level=config.LOG_LEVEL.lower()
        )
        
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
