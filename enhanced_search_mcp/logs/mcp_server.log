2025-08-05 10:29:27,564 ERROR:searx: Error while getting the version: fatal: not a git repository (or any parent up to mount point /mnt)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).

2025-08-05 10:29:27,568 ERROR:searx: Error while getting the git URL & branch: fatal: not a git repository (or any parent up to mount point /mnt)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).



╭─ FastMCP 2.0 ──────────────────────────────────────────────────────────────╮
│                                                                            │
│        _ __ ___ ______           __  __  _____________    ____    ____     │
│       _ __ ___ / ____/___ ______/ /_/  |/  / ____/ __ \  |___ \  / __ \    │
│      _ __ ___ / /_  / __ `/ ___/ __/ /|_/ / /   / /_/ /  ___/ / / / / /    │
│     _ __ ___ / __/ / /_/ (__  ) /_/ /  / / /___/ ____/  /  __/_/ /_/ /     │
│    _ __ ___ /_/    \__,_/____/\__/_/  /_/\____/_/      /_____(_)____/      │
│                                                                            │
│                                                                            │
│                                                                            │
│    🖥️  Server name:     Enhanced Search Engine MCP Server                   │
│    📦 Transport:       SSE                                                 │
│    🔗 Server URL:      http://0.0.0.0:8881/sse/                            │
│                                                                            │
│    📚 Docs:            https://gofastmcp.com                               │
│    🚀 Deploy:          https://fastmcp.cloud                               │
│                                                                            │
│    🏎️  FastMCP version: 2.11.0                                              │
│    🤝 MCP version:     1.12.3                                              │
│                                                                            │
╰────────────────────────────────────────────────────────────────────────────╯


[08/05/25 10:29:27] INFO     Starting MCP server 'Enhanced Search server.py:1519
                             Engine MCP Server' with transport                  
                             'sse' on http://0.0.0.0:8881/sse/                  
/home/<USER>/miniconda/envs/searxng/lib/python3.11/site-packages/websockets/legacy/__init__.py:6: DeprecationWarning: websockets.legacy is deprecated; see https://websockets.readthedocs.io/en/stable/howto/upgrade.html for upgrade instructions
  warnings.warn(  # deprecated in 14.0 - 2024-11-09
/home/<USER>/miniconda/envs/searxng/lib/python3.11/site-packages/uvicorn/protocols/websockets/websockets_impl.py:17: DeprecationWarning: websockets.server.WebSocketServerProtocol is deprecated
  from websockets.server import WebSocketServerProtocol
INFO:     Started server process [52050]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8881 (Press CTRL+C to quit)
INFO:     127.0.0.1:49926 - "GET /health HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49934 - "GET /health HTTP/1.1" 404 Not Found
