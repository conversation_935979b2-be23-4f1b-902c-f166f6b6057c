{"services": [{"name": "searxng", "type": "search_engine", "status": "running", "config": {"host": "127.0.0.1", "port": 8885, "bind_address": "127.0.0.1", "url": "http://localhost:8885", "access_level": "local_only", "secret_key": "auto_generated", "log_file": "logs/searxng.log", "settings_file": "searx/settings.yml"}, "endpoints": [{"path": "/", "method": "GET", "description": "SearXNG主页面"}, {"path": "/search", "method": "GET", "description": "搜索接口", "parameters": ["q", "format", "engines"]}], "environment_variables": {"SEARXNG_PORT": "8885", "SEARXNG_BIND_ADDRESS": "127.0.0.1", "SEARXNG_URL": "http://localhost:8885", "SEARXNG_SECRET": "auto_generated_32_char_hex"}}, {"name": "mcp_server", "type": "mcp_protocol_server", "status": "running", "config": {"host": "0.0.0.0", "port": 8881, "bind_address": "0.0.0.0", "wsl_ip": "**************", "access_level": "external", "protocol": "fastmcp", "transport": "sse", "log_file": "enhanced_search_mcp/logs/mcp_server.log", "server_file": "enhanced_search_mcp/server.py"}, "endpoints": [{"path": "/sse", "method": "GET", "description": "Server-Sent Events端点", "protocol": "SSE"}, {"path": "/messages/", "method": "POST", "description": "MCP消息处理端点", "content_type": "application/json"}], "tools": [{"name": "enhanced_search", "description": "执行增强搜索，支持多轮搜索策略", "parameters": [{"name": "query", "type": "string", "required": true, "description": "搜索查询内容"}, {"name": "depth", "type": "string", "default": "deep", "options": ["basic", "deep", "comprehensive"], "description": "搜索深度"}, {"name": "max_results", "type": "integer", "default": 20, "description": "最大结果数量"}]}, {"name": "basic_search", "description": "执行基础搜索", "parameters": [{"name": "query", "type": "string", "required": true, "description": "搜索查询内容"}, {"name": "engines", "type": "string", "default": "", "description": "搜索引擎列表（逗号分隔）"}, {"name": "max_results", "type": "integer", "default": 10, "description": "最大结果数量"}]}, {"name": "get_search_stats", "description": "获取搜索统计信息", "parameters": []}, {"name": "get_system_info", "description": "获取系统信息", "parameters": []}], "environment_variables": {"MCP_HOST": "0.0.0.0", "MCP_PORT": "8881", "LOG_LEVEL": "INFO", "SEARXNG_URL": "http://localhost:8885"}}], "network": {"wsl_ip": "**************", "docker_access": {"method": "host.docker.internal", "configuration": {"docker_compose": {"extra_hosts": ["host.docker.internal:host-gateway"], "environment": {"MCP_SERVER_URL": "http://host.docker.internal:8881/sse"}}, "docker_run": {"flags": ["--add-host host.docker.internal:host-gateway"], "environment": ["-e MCP_SERVER_URL=http://host.docker.internal:8881/sse"]}}}, "access_urls": {"searxng": {"local": "http://localhost:8885", "wsl": "http://127.0.0.1:8885", "external": "不可访问（仅本地）"}, "mcp_server": {"local": "http://localhost:8881", "wsl": "http://**************:8881", "docker": "http://host.docker.internal:8881", "sse_endpoint": "http://host.docker.internal:8881/sse"}}}, "conda_environment": {"name": "searxng", "path": "/home/<USER>/miniconda/envs/searxng", "python_version": "3.11.13", "activation_command": "conda activate /home/<USER>/miniconda/envs/searxng"}, "startup_scripts": [{"name": "start_simple.sh", "type": "bash", "description": "主启动脚本，处理conda环境和服务启动", "features": ["自动conda环境激活", "依赖检查和安装", "环境变量设置", "服务启动和监控"]}, {"name": "start_all_services.ps1", "type": "powershell", "description": "Windows PowerShell管理脚本", "features": ["通过WSL管理服务", "状态查看", "日志监控", "连通性测试"]}], "testing": {"scripts": [{"name": "test_mcp_simple.sh", "description": "简单MCP服务器测试", "tests": ["端口连接测试", "SSE端点测试", "进程状态检查", "SearXNG连通性"]}, {"name": "test_all_services.sh", "description": "综合服务测试", "tests": ["进程状态", "端口监听", "HTTP连接", "MCP功能", "搜索功能", "Docker访问", "性能测试"]}]}, "logs": {"searxng": {"file": "logs/searxng.log", "command": "tail -f logs/searxng.log"}, "mcp_server": {"file": "enhanced_search_mcp/logs/mcp_server.log", "command": "tail -f enhanced_search_mcp/logs/mcp_server.log"}, "startup": {"file": "service_startup.log", "command": "tail -f service_startup.log"}}, "management_commands": {"start": "./start_simple.sh", "test": "./test_mcp_simple.sh", "stop": {"searxng": "pkill -f 'python.*searx.webapp'", "mcp_server": "pkill -f 'python.*server.py'", "all": "pkill -f 'python.*searx.webapp' && pkill -f 'python.*server.py'"}, "status": {"processes": "ps aux | grep -E '(searx|server.py)'", "ports": "ss -tlnp | grep -E ':888[15]'"}}, "docker_llm_integration": {"recommended_config": {"mcp_server_url": "http://host.docker.internal:8881/sse", "docker_compose": {"version": "3.8", "services": {"your_llm": {"image": "your-llm-image:latest", "extra_hosts": ["host.docker.internal:host-gateway"], "environment": {"MCP_SERVER_URL": "http://host.docker.internal:8881/sse", "MCP_HEALTH_URL": "http://host.docker.internal:8881/sse"}, "restart": "unless-stopped"}}}}, "alternative_configs": [{"name": "direct_ip", "mcp_server_url": "http://**************:8881/sse", "note": "使用WSL IP直接访问"}, {"name": "host_network", "docker_network_mode": "host", "mcp_server_url": "http://localhost:8881/sse", "note": "使用Docker host网络模式"}]}, "deployment_status": {"overall": "success", "searxng": {"status": "running", "pid": "检查中", "listening": "127.0.0.1:8885"}, "mcp_server": {"status": "running", "pid": "检查中", "listening": "0.0.0.0:8881"}, "docker_accessibility": "confirmed", "last_tested": "2024-01-01T00:00:00Z"}}