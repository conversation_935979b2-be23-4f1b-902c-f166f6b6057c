[{"name": "enhanced_search", "type": "search", "description": "执行增强搜索，支持多轮搜索策略和结果优化", "parameters": [{"name": "query", "type": "string", "required": true, "description": "搜索查询内容", "example": "人工智能最新发展"}, {"name": "depth", "type": "enum", "required": false, "default": "deep", "options": ["basic", "deep", "comprehensive"], "description": "搜索深度级别", "details": {"basic": "快速搜索，返回基础结果", "deep": "深度搜索，包含多轮优化", "comprehensive": "全面搜索，最详细的结果"}}, {"name": "max_results", "type": "integer", "required": false, "default": 20, "min": 1, "max": 50, "description": "最大返回结果数量"}], "response_format": {"type": "formatted_text", "includes": ["search_summary", "results_list", "source_links"]}, "usage_example": {"query": "Python异步编程最佳实践", "depth": "deep", "max_results": 15}}, {"name": "basic_search", "type": "search", "description": "执行基础搜索，直接返回搜索引擎结果", "parameters": [{"name": "query", "type": "string", "required": true, "description": "搜索查询内容", "example": "Docker容器化部署"}, {"name": "engines", "type": "string", "required": false, "default": "", "description": "指定搜索引擎（逗号分隔）", "example": "google,bing,duckduckgo", "available_engines": ["google", "bing", "duckduck<PERSON>", "startpage", "searx", "yandex", "baidu", "github", "stackoverflow"]}, {"name": "max_results", "type": "integer", "required": false, "default": 10, "min": 1, "max": 30, "description": "最大返回结果数量"}], "response_format": {"type": "json", "structure": {"query": "string", "engines": "array", "results_count": "integer", "results": "array"}}, "usage_example": {"query": "机器学习算法比较", "engines": "google,stackoverflow", "max_results": 10}}, {"name": "get_search_stats", "type": "system", "description": "获取搜索引擎和MCP服务器的统计信息", "parameters": [], "response_format": {"type": "json", "includes": ["search_engine_stats", "adapter_stats", "performance_metrics", "error_counts"]}, "usage_example": "无需参数，直接调用"}, {"name": "get_system_info", "type": "system", "description": "获取系统运行状态和配置信息", "parameters": [], "response_format": {"type": "json", "includes": ["system_status", "service_health", "configuration_info", "network_status"]}, "usage_example": "无需参数，直接调用"}]