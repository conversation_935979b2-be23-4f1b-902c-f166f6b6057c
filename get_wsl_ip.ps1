# 获取WSL IP地址的PowerShell脚本
param(
    [switch]$ShowAll,
    [switch]$Help
)

function Show-Help {
    Write-Host "🌐 WSL IP地址查询工具" -ForegroundColor Cyan
    Write-Host "===================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "用法:" -ForegroundColor Yellow
    Write-Host "  .\get_wsl_ip.ps1 [选项]" -ForegroundColor White
    Write-Host ""
    Write-Host "选项:" -ForegroundColor Yellow
    Write-Host "  -ShowAll    显示所有网络接口信息" -ForegroundColor White
    Write-Host "  -Help       显示此帮助信息" -ForegroundColor White
}

if ($Help) {
    Show-Help
    exit 0
}

Write-Host "🌐 WSL网络配置信息" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan

# 方法1: 通过WSL命令获取IP
Write-Host "`n📍 方法1: WSL内部IP地址" -ForegroundColor Blue
try {
    $wslIPs = wsl hostname -I
    if ($wslIPs) {
        $ipList = $wslIPs.Trim() -split '\s+'
        foreach ($ip in $ipList) {
            if ($ip -match '^\d+\.\d+\.\d+\.\d+$') {
                Write-Host "   WSL IP: $ip" -ForegroundColor Green
            }
        }
    }
} catch {
    Write-Host "   ❌ 无法获取WSL IP地址" -ForegroundColor Red
}

# 方法2: 通过Windows网络适配器获取WSL网桥IP
Write-Host "`n📍 方法2: Windows侧WSL网桥IP" -ForegroundColor Blue
try {
    $wslAdapter = Get-NetAdapter | Where-Object { $_.Name -like "*WSL*" -or $_.InterfaceDescription -like "*WSL*" }
    if ($wslAdapter) {
        $wslIP = Get-NetIPAddress -InterfaceIndex $wslAdapter.InterfaceIndex -AddressFamily IPv4 | Select-Object -First 1
        if ($wslIP) {
            Write-Host "   Windows WSL网桥IP: $($wslIP.IPAddress)" -ForegroundColor Green
        }
    } else {
        Write-Host "   ⚠️ 未找到WSL网络适配器" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ❌ 无法获取WSL网桥IP" -ForegroundColor Red
}

# 方法3: 通过Docker Desktop网络获取
Write-Host "`n📍 方法3: Docker Desktop网络信息" -ForegroundColor Blue
try {
    $dockerNetworks = docker network ls --format "{{.Name}}" 2>$null
    if ($dockerNetworks) {
        Write-Host "   Docker网络列表:" -ForegroundColor Green
        foreach ($network in $dockerNetworks) {
            Write-Host "     - $network" -ForegroundColor Gray
        }
        
        # 检查host网络模式
        Write-Host "`n   🔍 检查Docker host网络访问:" -ForegroundColor Yellow
        $testResult = docker run --rm --network host alpine:latest ping -c 1 ********** 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "     ✅ Docker可以访问宿主机网络" -ForegroundColor Green
        } else {
            Write-Host "     ⚠️ Docker host网络访问可能受限" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "   ❌ Docker未运行或无法访问" -ForegroundColor Red
}

if ($ShowAll) {
    Write-Host "`n📍 详细网络接口信息" -ForegroundColor Blue
    Write-Host "=====================" -ForegroundColor Blue
    
    # 显示所有网络适配器
    Get-NetAdapter | Where-Object { $_.Status -eq "Up" } | ForEach-Object {
        Write-Host "`n接口: $($_.Name)" -ForegroundColor Cyan
        Write-Host "  描述: $($_.InterfaceDescription)" -ForegroundColor Gray
        Write-Host "  状态: $($_.Status)" -ForegroundColor Gray
        
        $ipAddresses = Get-NetIPAddress -InterfaceIndex $_.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue
        foreach ($ip in $ipAddresses) {
            Write-Host "  IP地址: $($ip.IPAddress)" -ForegroundColor Green
        }
    }
}

Write-Host "`n💡 使用建议:" -ForegroundColor Yellow
Write-Host "1. 在WSL中运行SearXNG+MCP服务器" -ForegroundColor Gray
Write-Host "2. 在Windows Docker中配置LLM访问WSL IP" -ForegroundColor Gray
Write-Host "3. 确保防火墙允许相关端口通信" -ForegroundColor Gray
Write-Host "4. 使用 'docker run --add-host host.docker.internal:host-gateway' 访问宿主机" -ForegroundColor Gray

Write-Host "`n🔧 测试命令:" -ForegroundColor Yellow
Write-Host "  测试WSL服务: curl http://WSL_IP:8885" -ForegroundColor Gray
Write-Host "  测试MCP服务: curl http://WSL_IP:8881/health" -ForegroundColor Gray
