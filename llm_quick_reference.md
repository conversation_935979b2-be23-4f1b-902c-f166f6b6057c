# 🚀 Enhanced Search工具 - LLM快速参考

## 🔧 工具配置
```json
{
  "name": "enhanced-search",
  "type": "sse", 
  "url": "http://host.docker.internal:8881/sse"
}
```

## ⚡ 快速决策树

```
用户问题 → 需要最新信息？
    ├─ 是 → 使用 enhanced_search
    └─ 否 → 可能过时信息？
        ├─ 是 → 考虑使用 enhanced_search
        └─ 否 → 使用已有知识
```

## 🎯 工具选择指南

| 用户需求 | 推荐工具 | 参数建议 |
|---------|---------|---------|
| 最新新闻/趋势 | enhanced_search | depth: "deep", results: 15-20 |
| 技术问题 | enhanced_search | depth: "comprehensive", results: 20-25 |
| 快速事实 | enhanced_search | depth: "basic", results: 5-10 |
| 产品信息 | enhanced_search | depth: "deep", results: 15-20 |
| 学术研究 | enhanced_search | depth: "comprehensive", results: 25-30 |
| 查询建议 | search_suggestions | 无额外参数 |

## 📊 参数速查表

### search_depth 选择
- **basic** (2-5s): 简单查询，快速响应
- **deep** (5-10s): 默认选择，平衡质量和速度 ⭐
- **comprehensive** (10-15s): 复杂研究，最全面

### max_results 选择
- **5-10**: 简单事实查询
- **15-20**: 一般信息需求 ⭐
- **25-30**: 复杂研究分析

## 🔍 查询优化技巧

### ✅ 好的查询示例
```
❌ "AI"
✅ "2024年人工智能最新发展趋势"

❌ "编程"  
✅ "Python Web开发最佳实践2024"

❌ "手机"
✅ "iPhone 15 Pro功能特性评测对比"
```

### 🎯 查询增强词汇
- **时间**: "2024年", "最新", "近期", "当前"
- **质量**: "最佳实践", "权威指南", "专业评测"
- **深度**: "详细分析", "全面介绍", "深入研究"

## 🚨 常见场景处理

### 场景1: 用户查询模糊
```
用户: "告诉我关于AI的事情"
处理: 
1. 使用 search_suggestions 获取建议
2. 基于建议使用 enhanced_search
3. 参数: {"query": "人工智能发展现状应用前景2024", "search_depth": "deep"}
```

### 场景2: 需要对比信息
```
用户: "哪个云服务商比较好？"
处理:
1. 使用 enhanced_search
2. 参数: {"query": "AWS Azure GCP云服务商对比评测2024", "search_depth": "comprehensive", "max_results": 25}
```

### 场景3: 技术故障排除
```
用户: "Docker容器启动失败怎么办？"
处理:
1. 使用 enhanced_search  
2. 参数: {"query": "Docker容器启动失败常见问题解决方案", "search_depth": "comprehensive", "max_results": 20}
```

## 📝 回答模板

### 标准回答结构
```
根据最新搜索结果，[直接回答]。

主要发现：
• [要点1 - 来源权威性]
• [要点2 - 具体数据/事实]  
• [要点3 - 时效性信息]

[补充说明/建议]

*信息来源：[权威网站]，更新时间：[时间范围]*
```

### 快速回答模板
```
[直接答案]

具体来说：[简要说明]

*基于最新搜索结果*
```

## ⚠️ 注意事项

### 必须搜索的信号词
- "最新", "现在", "目前", "今天"
- "2024", "今年", "近期"  
- "价格", "评测", "对比"
- "新闻", "动态", "趋势"

### 无需搜索的信号词
- "什么是", "定义", "概念"
- "历史", "起源", "发展过程"
- "如何理解", "解释一下"
- "写一个", "创作", "设计"

### 错误处理
```
搜索失败 → 说明原因 → 提供基于已有知识的回答 → 建议用户稍后重试
结果不相关 → 调整查询 → 重新搜索 → 如仍失败则使用已有知识
```

## 🎨 高级技巧

### 多轮搜索策略
1. **概况搜索**: 先了解整体情况
2. **细节搜索**: 深入具体问题
3. **验证搜索**: 交叉验证重要信息

### 结果质量评估
- 优先权威来源 (官方网站、知名媒体)
- 注意信息时效性
- 对比多个来源
- 标注不确定信息

---
**记住**: 合理使用搜索工具是提供高质量回答的关键！ 🚀
