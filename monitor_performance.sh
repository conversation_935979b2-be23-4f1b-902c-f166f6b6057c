#!/bin/bash

# MCP Server性能监控脚本

echo "📊 MCP Server性能监控"
echo "===================="

WSL_IP=$(hostname -I | awk '{print $1}')

# 测试响应时间
test_response_time() {
    local endpoint=$1
    local description=$2
    
    echo -n "测试 $description: "
    local start_time=$(date +%s.%N)
    
    if curl -s --connect-timeout 5 "$endpoint" > /dev/null; then
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc)
        local duration_ms=$(echo "$duration * 1000" | bc)
        echo "${duration_ms%.*}ms ✅"
    else
        echo "失败 ❌"
    fi
}

# 测试各个端点
test_response_time "http://localhost:8885" "SearXNG"
test_response_time "http://localhost:8881/sse" "MCP SSE端点"

# 测试搜索性能
echo -e "\n🔍 搜索性能测试:"

# 基础搜索测试
echo -n "基础搜索测试: "
start_time=$(date +%s.%N)
response=$(curl -s -X POST http://localhost:8881/search \
    -H "Content-Type: application/json" \
    -d '{"query":"test","max_results":3}' \
    --connect-timeout 10)

if [ $? -eq 0 ]; then
    end_time=$(date +%s.%N)
    duration=$(echo "$end_time - $start_time" | bc)
    duration_ms=$(echo "$duration * 1000" | bc)
    echo "${duration_ms%.*}ms ✅"
else
    echo "失败 ❌"
fi

# 显示系统资源使用
echo -e "\n💻 系统资源使用:"
echo "CPU使用率:"
top -bn1 | grep "python.*server.py" | head -3

echo -e "\n内存使用:"
ps aux | grep "python.*server.py" | awk '{print "  PID: " $2 ", 内存: " $4 "%, 命令: " $11}'

echo -e "\n🔗 连接状态:"
ss -tn | grep :8881 | wc -l | xargs echo "活跃连接数:"

echo -e "\n📝 最近日志 (最后5行):"
tail -5 enhanced_search_mcp/logs/mcp_server.log
