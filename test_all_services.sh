#!/bin/bash

# 测试SearXNG和MCP Server的综合测试脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

echo -e "${CYAN}🧪 SearXNG + MCP Server 综合测试${NC}"
echo -e "${CYAN}==============================${NC}"

# 获取WSL IP
WSL_IP=$(hostname -I | awk '{print $1}')
echo -e "${BLUE}WSL IP地址: $WSL_IP${NC}"

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "\n${YELLOW}测试 $TOTAL_TESTS: $test_name${NC}"
    
    if eval "$test_command"; then
        echo -e "${GREEN}✅ 通过${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo -e "${RED}❌ 失败${NC}"
        return 1
    fi
}

# 1. 测试进程状态
echo -e "\n${PURPLE}📊 进程状态测试${NC}"
echo -e "${PURPLE}===============${NC}"

run_test "SearXNG进程检查" "pgrep -f 'python.*searx.webapp' > /dev/null" ""
run_test "MCP Server进程检查" "pgrep -f 'python.*server.py' > /dev/null" ""

# 2. 测试端口监听
echo -e "\n${PURPLE}🔌 端口监听测试${NC}"
echo -e "${PURPLE}===============${NC}"

run_test "SearXNG端口8885监听" "netstat -ln | grep ':8885 ' > /dev/null" ""
run_test "MCP Server端口8881监听" "netstat -ln | grep ':8881 ' > /dev/null" ""

# 3. 测试HTTP连接
echo -e "\n${PURPLE}🌐 HTTP连接测试${NC}"
echo -e "${PURPLE}===============${NC}"

run_test "SearXNG本地连接" "curl -s --connect-timeout 5 http://localhost:8885 > /dev/null" ""
run_test "SearXNG WSL IP连接" "curl -s --connect-timeout 5 http://$WSL_IP:8885 > /dev/null" ""
run_test "MCP Server本地连接" "curl -s --connect-timeout 5 http://localhost:8881/health > /dev/null" ""
run_test "MCP Server WSL IP连接" "curl -s --connect-timeout 5 http://$WSL_IP:8881/health > /dev/null" ""

# 4. 测试MCP功能
echo -e "\n${PURPLE}🤖 MCP功能测试${NC}"
echo -e "${PURPLE}==============${NC}"

# 测试健康检查
test_health_check() {
    local response=$(curl -s http://localhost:8881/health)
    echo "$response" | grep -q '"status":"healthy"'
}
run_test "MCP健康检查" "test_health_check" ""

# 测试工具列表
test_tools_list() {
    local response=$(curl -s http://localhost:8881/tools)
    echo "$response" | grep -q '"tools"'
}
run_test "MCP工具列表" "test_tools_list" ""

# 5. 测试搜索功能
echo -e "\n${PURPLE}🔍 搜索功能测试${NC}"
echo -e "${PURPLE}===============${NC}"

# 测试SearXNG搜索
test_searxng_search() {
    local response=$(curl -s "http://localhost:8885/search?q=test&format=json")
    echo "$response" | grep -q '"results"'
}
run_test "SearXNG搜索功能" "test_searxng_search" ""

# 测试MCP搜索
test_mcp_search() {
    local response=$(curl -s -X POST http://localhost:8881/search \
        -H "Content-Type: application/json" \
        -d '{"query":"test","max_results":3}')
    echo "$response" | grep -q '"results"'
}
run_test "MCP搜索功能" "test_mcp_search" ""

# 6. 测试Docker访问（如果Docker可用）
echo -e "\n${PURPLE}🐳 Docker访问测试${NC}"
echo -e "${PURPLE}===============${NC}"

if command -v docker &> /dev/null; then
    # 测试Docker host.docker.internal访问
    test_docker_access() {
        docker run --rm --add-host host.docker.internal:host-gateway \
            alpine:latest wget -q --spider http://host.docker.internal:8881/health 2>/dev/null
    }
    run_test "Docker host.docker.internal访问" "test_docker_access" ""
    
    # 测试Docker直接IP访问
    test_docker_ip_access() {
        docker run --rm alpine:latest \
            wget -q --spider http://$WSL_IP:8881/health 2>/dev/null
    }
    run_test "Docker直接IP访问" "test_docker_ip_access" ""
else
    echo -e "${YELLOW}⚠️ Docker未安装，跳过Docker测试${NC}"
fi

# 7. 性能测试
echo -e "\n${PURPLE}⚡ 性能测试${NC}"
echo -e "${PURPLE}==========${NC}"

# 测试响应时间
test_response_time() {
    local start_time=$(date +%s.%N)
    curl -s http://localhost:8881/health > /dev/null
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc)
    local duration_ms=$(echo "$duration * 1000" | bc)
    echo -e "${GRAY}响应时间: ${duration_ms%.*}ms${NC}"
    # 如果响应时间小于1秒则通过
    (( $(echo "$duration < 1.0" | bc -l) ))
}
run_test "MCP响应时间测试" "test_response_time" ""

# 8. 日志文件检查
echo -e "\n${PURPLE}📝 日志文件测试${NC}"
echo -e "${PURPLE}===============${NC}"

run_test "SearXNG日志文件存在" "test -f logs/searxng.log" ""
run_test "MCP Server日志文件存在" "test -f enhanced_search_mcp/logs/mcp_server.log" ""

# 检查日志中是否有错误
test_searxng_log_errors() {
    if [ -f logs/searxng.log ]; then
        local error_count=$(grep -i "error\|exception\|failed" logs/searxng.log | wc -l)
        echo -e "${GRAY}SearXNG日志错误数: $error_count${NC}"
        [ "$error_count" -lt 5 ]  # 允许少量错误
    else
        return 1
    fi
}
run_test "SearXNG日志错误检查" "test_searxng_log_errors" ""

test_mcp_log_errors() {
    if [ -f enhanced_search_mcp/logs/mcp_server.log ]; then
        local error_count=$(grep -i "error\|exception\|failed" enhanced_search_mcp/logs/mcp_server.log | wc -l)
        echo -e "${GRAY}MCP日志错误数: $error_count${NC}"
        [ "$error_count" -lt 5 ]  # 允许少量错误
    else
        return 1
    fi
}
run_test "MCP Server日志错误检查" "test_mcp_log_errors" ""

# 测试结果汇总
echo -e "\n${CYAN}📊 测试结果汇总${NC}"
echo -e "${CYAN}===============${NC}"

SUCCESS_RATE=$(echo "scale=1; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc)

echo -e "${BLUE}总测试数: $TOTAL_TESTS${NC}"
echo -e "${GREEN}通过测试: $PASSED_TESTS${NC}"
echo -e "${RED}失败测试: $((TOTAL_TESTS - PASSED_TESTS))${NC}"
echo -e "${PURPLE}成功率: $SUCCESS_RATE%${NC}"

if [ "$PASSED_TESTS" -eq "$TOTAL_TESTS" ]; then
    echo -e "\n${GREEN}🎉 所有测试通过！服务运行正常${NC}"
    exit 0
elif [ "$SUCCESS_RATE" -ge "80" ]; then
    echo -e "\n${YELLOW}⚠️ 大部分测试通过，服务基本正常${NC}"
    exit 0
else
    echo -e "\n${RED}❌ 多个测试失败，请检查服务状态${NC}"
    echo -e "\n${YELLOW}故障排除建议:${NC}"
    echo -e "${GRAY}1. 检查服务是否正在运行: ps aux | grep -E '(searx|server.py)'${NC}"
    echo -e "${GRAY}2. 查看服务日志: tail -20 logs/searxng.log${NC}"
    echo -e "${GRAY}3. 查看MCP日志: tail -20 enhanced_search_mcp/logs/mcp_server.log${NC}"
    echo -e "${GRAY}4. 重启服务: ./start_all_services.sh${NC}"
    exit 1
fi
