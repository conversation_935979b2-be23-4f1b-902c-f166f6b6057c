#!/bin/bash

# MCP Server网络配置脚本
# 专门为Docker中的LLM访问WSL中的MCP Server配置网络

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}🤖 MCP Server网络配置脚本${NC}"
echo -e "${CYAN}=========================${NC}"

# 获取WSL IP地址
echo -e "${BLUE}📍 获取WSL网络信息...${NC}"
WSL_IPS=$(hostname -I)
PRIMARY_IP=$(echo $WSL_IPS | awk '{print $1}')
echo -e "${GREEN}WSL主要IP地址: $PRIMARY_IP${NC}"

# 检查MCP Server端口
echo -e "${BLUE}🔌 检查MCP Server端口状态...${NC}"
if netstat -ln | grep -q ":8881 "; then
    echo -e "${GREEN}✅ MCP Server端口8881正在监听${NC}"
    # 显示监听详情
    netstat -ln | grep ":8881 " | while read line; do
        if [[ $line == *"0.0.0.0:8881"* ]]; then
            echo -e "${GREEN}   ✅ 监听所有接口: $line${NC}"
        elif [[ $line == *"127.0.0.1:8881"* ]]; then
            echo -e "${YELLOW}   ⚠️ 仅监听本地: $line${NC}"
            echo -e "${YELLOW}   需要配置MCP_HOST=0.0.0.0以接受外部访问${NC}"
        else
            echo -e "${BLUE}   $line${NC}"
        fi
    done
else
    echo -e "${YELLOW}⚠️ MCP Server端口8881未在监听${NC}"
    echo -e "${YELLOW}请先启动MCP Server: ./scripts/start_searxng_with_mcp.sh${NC}"
fi

# 检查SearXNG端口（仅供参考）
echo -e "${BLUE}🔍 检查SearXNG端口状态...${NC}"
if netstat -ln | grep -q ":8885 "; then
    echo -e "${GREEN}✅ SearXNG端口8885正在监听${NC}"
    netstat -ln | grep ":8885 " | while read line; do
        echo -e "${BLUE}   $line${NC}"
    done
else
    echo -e "${YELLOW}⚠️ SearXNG端口8885未在监听${NC}"
fi

# 配置防火墙（如果需要）
echo -e "${BLUE}🔥 配置防火墙...${NC}"
if command -v ufw &> /dev/null; then
    UFW_STATUS=$(sudo ufw status | head -1)
    if [[ "$UFW_STATUS" == *"active"* ]]; then
        echo -e "${YELLOW}配置UFW防火墙规则...${NC}"
        sudo ufw allow 8881/tcp comment "MCP Server for Docker LLM"
        echo -e "${GREEN}✅ MCP Server防火墙规则已添加${NC}"
    else
        echo -e "${BLUE}UFW未激活，跳过防火墙配置${NC}"
    fi
else
    echo -e "${BLUE}UFW未安装，跳过防火墙配置${NC}"
fi

# 测试MCP Server连通性
echo -e "${BLUE}🧪 测试MCP Server连通性...${NC}"

test_mcp_connection() {
    local host=$1
    local description=$2
    local url="http://$host:8881/health"
    
    echo -n "测试 $description ($url): "
    if curl -s --connect-timeout 5 "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 成功${NC}"
        return 0
    else
        echo -e "${RED}❌ 失败${NC}"
        return 1
    fi
}

# 测试本地连接
test_mcp_connection "localhost" "本地连接"

# 测试WSL IP连接
test_mcp_connection "$PRIMARY_IP" "WSL IP连接"

# 测试Docker访问（如果Docker可用）
echo -e "${BLUE}🐳 测试Docker容器访问...${NC}"
if command -v docker &> /dev/null; then
    echo -n "测试Docker host.docker.internal访问: "
    if docker run --rm --add-host host.docker.internal:host-gateway alpine:latest wget -q --spider http://host.docker.internal:8881/health 2>/dev/null; then
        echo -e "${GREEN}✅ 成功${NC}"
    else
        echo -e "${RED}❌ 失败${NC}"
        echo -e "${YELLOW}   可能原因: MCP Server未启动或未监听0.0.0.0${NC}"
    fi
    
    echo -n "测试Docker直接IP访问: "
    if docker run --rm alpine:latest wget -q --spider http://$PRIMARY_IP:8881/health 2>/dev/null; then
        echo -e "${GREEN}✅ 成功${NC}"
    else
        echo -e "${RED}❌ 失败${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ Docker未安装，跳过Docker测试${NC}"
fi

# 生成LLM配置建议
echo -e "\n${CYAN}🤖 LLM配置建议${NC}"
echo -e "${CYAN}===============${NC}"

echo -e "${YELLOW}推荐配置 (使用host.docker.internal):${NC}"
echo -e "${GREEN}MCP Server地址: http://host.docker.internal:8881${NC}"
echo -e "${BLUE}Docker运行命令添加: --add-host host.docker.internal:host-gateway${NC}"

echo -e "\n${YELLOW}备选配置 (直接使用WSL IP):${NC}"
echo -e "${GREEN}MCP Server地址: http://$PRIMARY_IP:8881${NC}"

echo -e "\n${YELLOW}Docker Compose配置示例:${NC}"
cat << EOF
services:
  your-llm:
    image: your-llm-image
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - MCP_SERVER_URL=http://host.docker.internal:8881
EOF

echo -e "\n${YELLOW}Docker run命令示例:${NC}"
echo -e "${GREEN}docker run --add-host host.docker.internal:host-gateway -e MCP_SERVER_URL=http://host.docker.internal:8881 your-llm-image${NC}"

# 生成测试脚本
echo -e "\n${CYAN}🧪 生成MCP测试脚本${NC}"
echo -e "${CYAN}==================${NC}"

cat > test_mcp_access.sh << EOF
#!/bin/bash
# MCP Server访问测试脚本

echo "🧪 测试MCP Server访问"
echo "==================="

WSL_IP=\$(hostname -I | awk '{print \$1}')
echo "WSL IP: \$WSL_IP"

# 测试健康检查
echo -n "健康检查 (localhost): "
if curl -s http://localhost:8881/health > /dev/null; then
    echo "✅ 成功"
else
    echo "❌ 失败"
fi

echo -n "健康检查 (WSL IP): "
if curl -s http://\$WSL_IP:8881/health > /dev/null; then
    echo "✅ 成功"
else
    echo "❌ 失败"
fi

# 测试MCP工具列表
echo -n "MCP工具列表: "
if curl -s http://localhost:8881/tools > /dev/null; then
    echo "✅ 成功"
    echo "可用工具:"
    curl -s http://localhost:8881/tools | python3 -m json.tool 2>/dev/null | grep '"name"' | head -5
else
    echo "❌ 失败"
fi

# 测试搜索功能
echo -n "搜索功能测试: "
if curl -s -X POST http://localhost:8881/search -H "Content-Type: application/json" -d '{"query":"test"}' > /dev/null; then
    echo "✅ 成功"
else
    echo "❌ 失败"
fi

# 测试Docker访问
if command -v docker &> /dev/null; then
    echo -n "Docker容器访问测试: "
    if docker run --rm --add-host host.docker.internal:host-gateway alpine:latest wget -q --spider http://host.docker.internal:8881/health 2>/dev/null; then
        echo "✅ 成功"
    else
        echo "❌ 失败"
    fi
else
    echo "⚠️ Docker未安装，跳过Docker测试"
fi
EOF

chmod +x test_mcp_access.sh
echo -e "${GREEN}✅ MCP测试脚本已生成: test_mcp_access.sh${NC}"

echo -e "\n${CYAN}📋 操作总结${NC}"
echo -e "${CYAN}=========${NC}"
echo -e "${GREEN}✅ MCP Server网络配置完成${NC}"
echo -e "${BLUE}🎯 LLM只需访问: http://host.docker.internal:8881${NC}"
echo -e "${BLUE}🔧 运行测试: ./test_mcp_access.sh${NC}"
echo -e "${BLUE}🚀 启动服务: ./scripts/start_searxng_with_mcp.sh${NC}"

echo -e "\n${YELLOW}💡 关键要点:${NC}"
echo -e "${GRAY}1. SearXNG只需本地访问(127.0.0.1:8885)${NC}"
echo -e "${GRAY}2. MCP Server监听所有接口(0.0.0.0:8881)${NC}"
echo -e "${GRAY}3. LLM通过host.docker.internal:8881访问MCP${NC}"
echo -e "${GRAY}4. MCP Server内部调用SearXNG进行搜索${NC}"
