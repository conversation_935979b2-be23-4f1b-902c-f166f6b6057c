# 🚀 SearXNG + MCP Server 启动指南

## 📋 概述

本指南提供了同时启动SearXNG搜索引擎和MCP服务器的完整解决方案，支持Windows + WSL环境，让Docker中的LLM能够访问搜索服务。

## 🏗️ 架构图

```
Windows Host
├── PowerShell脚本 (start_all_services.ps1)
└── WSL2 环境
    ├── SearXNG (localhost:8885) 
    ├── MCP Server (0.0.0.0:8881)
    └── Bash脚本 (start_all_services.sh)

Docker LLM → host.docker.internal:8881 → WSL MCP Server → localhost:8885 SearXNG
```

## 🛠️ 提供的脚本

### 1. **start_all_services.sh** (WSL Bash脚本)
- 自动激活conda searxng环境
- 同时启动SearXNG和MCP Server
- 实时监控服务状态
- 优雅的错误处理和清理

### 2. **start_all_services.ps1** (Windows PowerShell脚本)
- 通过WSL管理服务
- 提供状态查看、日志监控、测试功能
- 友好的Windows用户界面

### 3. **test_all_services.sh** (综合测试脚本)
- 全面的服务健康检查
- 性能测试和连通性验证
- Docker访问测试

## 🚀 快速启动

### 方法1: 使用PowerShell脚本 (推荐)

```powershell
# 启动所有服务
.\start_all_services.ps1

# 查看服务状态
.\start_all_services.ps1 -Status

# 查看服务日志
.\start_all_services.ps1 -Logs

# 测试服务连通性
.\start_all_services.ps1 -Test

# 停止所有服务
.\start_all_services.ps1 -Stop

# 获取帮助
.\start_all_services.ps1 -Help
```

### 方法2: 直接使用WSL脚本

```bash
# 在WSL中执行
./start_all_services.sh

# 或者从Windows执行
wsl ./start_all_services.sh
```

## 📊 服务验证

### 自动测试
```bash
# 运行综合测试
./test_all_services.sh
```

### 手动验证
```bash
# 检查进程
ps aux | grep -E "(searx|server.py)"

# 检查端口
netstat -ln | grep -E ":888[15]"

# 测试连接
curl http://localhost:8885          # SearXNG
curl http://localhost:8881/health   # MCP Server
```

## 🌐 网络配置

### WSL网络地址
- **SearXNG**: `http://localhost:8885` (仅本地访问)
- **MCP Server**: `http://WSL_IP:8881` (外部可访问)

### Docker LLM配置
```yaml
# Docker Compose
services:
  your-llm:
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - MCP_SERVER_URL=http://host.docker.internal:8881
```

```bash
# Docker Run
docker run --add-host host.docker.internal:host-gateway \
  -e MCP_SERVER_URL=http://host.docker.internal:8881 \
  your-llm-image
```

## 📝 日志文件

- **SearXNG日志**: `logs/searxng.log`
- **MCP Server日志**: `enhanced_search_mcp/logs/mcp_server.log`
- **启动日志**: `service_startup.log`

### 查看日志
```bash
# 实时查看SearXNG日志
tail -f logs/searxng.log

# 实时查看MCP日志
tail -f enhanced_search_mcp/logs/mcp_server.log

# 查看最近的错误
grep -i error logs/searxng.log | tail -10
```

## 🔧 环境变量配置

脚本会自动设置以下环境变量：

```bash
export SEARXNG_PORT="8885"
export SEARXNG_BIND_ADDRESS="127.0.0.1"  # 仅本地访问
export SEARXNG_URL="http://localhost:8885"
export MCP_HOST="0.0.0.0"                # 监听所有接口
export MCP_PORT="8881"
export LOG_LEVEL="INFO"
```

## 🐛 故障排除

### 常见问题

1. **conda环境问题**
   ```bash
   # 手动创建环境
   conda create -n searxng python=3.11 -y
   conda activate searxng
   ```

2. **端口占用**
   ```bash
   # 查看端口占用
   lsof -i :8885
   lsof -i :8881
   
   # 强制释放端口
   lsof -ti:8885 | xargs kill -9
   lsof -ti:8881 | xargs kill -9
   ```

3. **依赖缺失**
   ```bash
   # 重新安装依赖
   pip install -r requirements.txt
   pip install -r enhanced_search_mcp/requirements.txt
   ```

4. **服务启动失败**
   ```bash
   # 查看详细错误
   tail -50 logs/searxng.log
   tail -50 enhanced_search_mcp/logs/mcp_server.log
   ```

### 重置服务
```bash
# 完全重置
.\start_all_services.ps1 -Stop
sleep 5
.\start_all_services.ps1
```

## 📈 性能优化

### 建议配置
- **并发搜索数**: 5 (MAX_CONCURRENT_SEARCHES)
- **缓存TTL**: 3600秒 (1小时)
- **最大结果数**: 20 (MAX_TOTAL_RESULTS)

### 监控指标
- 响应时间 < 1秒
- 成功率 > 95%
- 内存使用 < 500MB

## 🔒 安全考虑

1. **网络隔离**: SearXNG只监听localhost
2. **访问控制**: MCP Server通过环境变量配置
3. **日志管理**: 定期清理日志文件
4. **依赖更新**: 定期更新Python包

## 📞 支持

如果遇到问题：

1. 运行测试脚本: `./test_all_services.sh`
2. 查看日志文件
3. 检查网络配置
4. 验证conda环境

## 🎯 下一步

服务启动后，你可以：

1. **配置LLM**: 使用 `http://host.docker.internal:8881` 作为MCP服务器地址
2. **测试搜索**: 访问 `http://WSL_IP:8881/tools` 查看可用工具
3. **监控性能**: 定期运行测试脚本验证服务状态
4. **扩展功能**: 根据需要调整搜索引擎和参数配置
