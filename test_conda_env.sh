#!/bin/bash

# Conda环境测试脚本
echo "🧪 Conda环境诊断脚本"
echo "==================="

# 检查conda路径
echo "1. 检查conda安装路径:"
if [ -f ~/miniconda3/etc/profile.d/conda.sh ]; then
    echo "✅ 找到miniconda: ~/miniconda3/etc/profile.d/conda.sh"
    CONDA_SCRIPT="~/miniconda3/etc/profile.d/conda.sh"
elif [ -f ~/anaconda3/etc/profile.d/conda.sh ]; then
    echo "✅ 找到anaconda: ~/anaconda3/etc/profile.d/conda.sh"
    CONDA_SCRIPT="~/anaconda3/etc/profile.d/conda.sh"
else
    echo "❌ 未找到conda安装"
    exit 1
fi

# 初始化conda
echo -e "\n2. 初始化conda:"
source ~/miniconda3/etc/profile.d/conda.sh 2>/dev/null || source ~/anaconda3/etc/profile.d/conda.sh
echo "✅ conda脚本已加载"

# 检查conda命令
echo -e "\n3. 检查conda命令:"
if command -v conda &> /dev/null; then
    echo "✅ conda命令可用"
    conda --version
else
    echo "❌ conda命令不可用"
    exit 1
fi

# 列出环境
echo -e "\n4. 列出conda环境:"
conda env list

# 检查searxng环境
echo -e "\n5. 检查searxng环境:"
if conda env list | grep -q "searxng"; then
    echo "✅ searxng环境存在"
    
    # 尝试激活环境
    echo -e "\n6. 尝试激活searxng环境:"
    conda activate searxng
    
    if [[ "$CONDA_DEFAULT_ENV" == "searxng" ]]; then
        echo "✅ searxng环境激活成功"
        echo "当前环境: $CONDA_DEFAULT_ENV"
        echo "Python版本: $(python --version)"
        echo "Python路径: $(which python)"
        
        # 检查关键包
        echo -e "\n7. 检查关键Python包:"
        packages=("flask" "werkzeug" "babel" "lxml" "httpx" "fastapi" "uvicorn" "aiohttp")
        for pkg in "${packages[@]}"; do
            if python -c "import $pkg" 2>/dev/null; then
                echo "✅ $pkg"
            else
                echo "❌ $pkg (缺失)"
            fi
        done
        
    else
        echo "❌ 环境激活失败"
        echo "当前环境: $CONDA_DEFAULT_ENV"
    fi
else
    echo "❌ searxng环境不存在"
    echo "创建环境: conda create -n searxng python=3.11 -y"
fi

echo -e "\n📋 诊断完成"
