{"mcp_servers": [{"name": "enhanced-search", "type": "sse", "url": "http://host.docker.internal:8881/sse", "description": "增强搜索引擎MCP服务器 - 提供高质量网络搜索能力", "capabilities": {"tools": [{"name": "enhanced_search", "description": "执行增强搜索，获取高质量网络信息", "priority": "high", "use_cases": ["最新信息查询", "技术问题解答", "学术研究", "产品信息", "新闻事件", "数据统计"], "parameters": {"query": {"type": "string", "required": true, "description": "搜索查询内容", "examples": ["Python 3.12新特性", "2024年人工智能发展趋势", "Docker容器化最佳实践"]}, "search_depth": {"type": "enum", "required": false, "default": "deep", "options": ["basic", "deep", "comprehensive"], "description": "搜索深度级别", "recommendations": {"basic": "快速查询，2-5秒响应", "deep": "平衡选择，5-10秒响应（推荐）", "comprehensive": "全面搜索，10-15秒响应"}}, "max_results": {"type": "integer", "required": false, "default": 20, "min": 1, "max": 50, "description": "最大结果数量", "recommendations": {"5-10": "简单事实查询", "15-20": "一般信息需求", "25-30": "复杂研究分析"}}, "enable_cache": {"type": "boolean", "required": false, "default": true, "description": "是否启用缓存"}}}, {"name": "search_suggestions", "description": "获取搜索建议，帮助优化查询", "priority": "medium", "use_cases": ["查询不够具体时", "需要扩展搜索思路", "探索相关主题"], "parameters": {"query": {"type": "string", "required": true, "description": "原始查询内容"}}}, {"name": "search_stats", "description": "获取搜索统计信息", "priority": "low", "use_cases": ["检查服务状态", "性能监控", "调试问题"], "parameters": {}}, {"name": "system_info", "description": "获取系统信息", "priority": "low", "use_cases": ["技术支持", "系统诊断", "配置查询"], "parameters": {}}]}, "usage_guidelines": {"when_to_use": ["用户询问最新信息、新闻、趋势", "需要具体数据、统计信息、事实", "技术问题需要最新解决方案", "产品信息、价格、评测", "学术研究、论文、报告", "当前事件、时事新闻", "地理信息、天气、交通", "法律法规、政策变化"], "when_not_to_use": ["基础常识问题", "数学计算、逻辑推理", "创意写作、故事创作", "个人观点、建议类问题", "已有充分知识的历史事件"], "best_practices": ["使用具体、专业的搜索词", "包含时间限定词（如'2024年'、'最新'）", "避免过于宽泛的查询", "根据需求选择合适的搜索深度", "优先引用权威来源", "注明信息的时效性"]}, "performance": {"response_times": {"basic": "2-5 seconds", "deep": "5-10 seconds", "comprehensive": "10-15 seconds"}, "concurrent_limit": 5, "timeout": 30, "cache_ttl": 3600}}], "integration_config": {"docker_requirements": {"extra_hosts": ["host.docker.internal:host-gateway"], "environment": {"MCP_SERVER_URL": "http://host.docker.internal:8881/sse"}}, "fallback_urls": ["http://**************:8881/sse"], "health_check": {"endpoint": "/sse", "timeout": 5, "retry_attempts": 3}}, "prompt_templates": {"system_prompt_addition": "你拥有强大的网络搜索能力，可以通过enhanced_search工具获取最新、准确的信息。当用户询问需要最新信息的问题时，主动使用搜索工具。优先使用deep搜索深度，根据查询复杂度调整max_results参数。", "tool_usage_examples": [{"scenario": "用户询问最新技术", "user_query": "最新的AI发展趋势是什么？", "tool_call": {"tool": "enhanced_search", "parameters": {"query": "2024年人工智能发展趋势最新动态", "search_depth": "deep", "max_results": 20}}}, {"scenario": "技术问题解答", "user_query": "如何优化React应用性能？", "tool_call": {"tool": "enhanced_search", "parameters": {"query": "React应用性能优化最佳实践2024", "search_depth": "comprehensive", "max_results": 25}}}, {"scenario": "快速事实查询", "user_query": "今天的天气怎么样？", "tool_call": {"tool": "enhanced_search", "parameters": {"query": "今日天气预报", "search_depth": "basic", "max_results": 5}}}]}, "monitoring": {"log_tool_usage": true, "track_performance": true, "alert_on_failures": true, "metrics_to_track": ["tool_call_frequency", "response_times", "success_rates", "query_types"]}}