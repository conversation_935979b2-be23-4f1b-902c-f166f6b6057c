#!/bin/bash

# 同时启动SearXNG和MCP Server的综合脚本
# 支持conda环境自动激活和服务管理

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

# 脚本信息
echo -e "${CYAN}🚀 SearXNG + MCP Server 综合启动脚本${NC}"
echo -e "${CYAN}=======================================${NC}"

# 获取脚本目录和项目目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$SCRIPT_DIR"
MCP_DIR="$PROJECT_DIR/enhanced_search_mcp"

echo -e "${BLUE}项目目录: $PROJECT_DIR${NC}"
echo -e "${BLUE}MCP目录: $MCP_DIR${NC}"

# 检查必要目录
if [ ! -d "$MCP_DIR" ]; then
    echo -e "${RED}❌ MCP目录不存在: $MCP_DIR${NC}"
    exit 1
fi

# 初始化conda环境
echo -e "${YELLOW}🐍 初始化conda环境...${NC}"

# 强制初始化conda，无论是否已经可用
if [ -f ~/miniconda3/etc/profile.d/conda.sh ]; then
    source ~/miniconda3/etc/profile.d/conda.sh
    echo -e "${GREEN}✅ 从miniconda初始化conda${NC}"
elif [ -f ~/anaconda3/etc/profile.d/conda.sh ]; then
    source ~/anaconda3/etc/profile.d/conda.sh
    echo -e "${GREEN}✅ 从anaconda初始化conda${NC}"
else
    echo -e "${RED}❌ 找不到conda安装${NC}"
    echo -e "${YELLOW}请确保conda已安装在 ~/miniconda3 或 ~/anaconda3${NC}"
    exit 1
fi

# 验证conda命令现在可用
if command -v conda &> /dev/null; then
    echo -e "${GREEN}✅ conda命令现在可用${NC}"
else
    echo -e "${RED}❌ conda初始化失败${NC}"
    exit 1
fi

# 检查并创建searxng环境
echo -e "${YELLOW}🔍 检查searxng conda环境...${NC}"
if conda env list | grep -q "searxng"; then
    echo -e "${GREEN}✅ searxng环境存在${NC}"
else
    echo -e "${YELLOW}正在创建searxng环境...${NC}"
    conda create -n searxng python=3.11 -y
    echo -e "${GREEN}✅ searxng环境创建完成${NC}"
fi

# 激活searxng环境
echo -e "${YELLOW}🔄 激活searxng环境...${NC}"
conda activate searxng

# 验证环境激活
CURRENT_ENV=$(conda info --envs | grep '*' | awk '{print $1}')
if [ "$CURRENT_ENV" = "searxng" ]; then
    echo -e "${GREEN}✅ 已激活searxng环境${NC}"
else
    echo -e "${RED}❌ 环境激活失败，当前环境: $CURRENT_ENV${NC}"
    exit 1
fi

# 安装依赖
echo -e "${YELLOW}📦 检查并安装依赖...${NC}"
cd "$PROJECT_DIR"

# 安装SearXNG依赖
if [ -f "requirements.txt" ]; then
    echo -e "${BLUE}安装SearXNG依赖...${NC}"
    pip install -r requirements.txt --quiet
else
    echo -e "${YELLOW}⚠️ 未找到SearXNG requirements.txt${NC}"
fi

# 安装MCP服务器依赖
if [ -f "enhanced_search_mcp/requirements.txt" ]; then
    echo -e "${BLUE}安装MCP服务器依赖...${NC}"
    pip install -r enhanced_search_mcp/requirements.txt --quiet
else
    echo -e "${YELLOW}⚠️ 安装基础MCP依赖...${NC}"
    pip install fastmcp fastapi uvicorn aiohttp python-dotenv --quiet
fi

# 验证关键依赖
echo -e "${YELLOW}🔍 验证关键依赖...${NC}"
check_package() {
    if python -c "import $1" 2>/dev/null; then
        echo -e "${GREEN}  ✅ $1${NC}"
        return 0
    else
        echo -e "${RED}  ❌ $1${NC}"
        return 1
    fi
}

MISSING_PACKAGES=()
for pkg in "flask" "werkzeug" "babel" "lxml" "httpx" "fastapi" "uvicorn" "aiohttp"; do
    if ! check_package "$pkg"; then
        MISSING_PACKAGES+=("$pkg")
    fi
done

if [ ${#MISSING_PACKAGES[@]} -gt 0 ]; then
    echo -e "${YELLOW}📦 安装缺失的包: ${MISSING_PACKAGES[*]}${NC}"
    pip install "${MISSING_PACKAGES[@]}"
fi

# 清理旧进程
echo -e "${YELLOW}🛑 清理旧进程...${NC}"
pkill -f "python.*searx.webapp" || true
pkill -f "python.*server.py" || true
pkill -f "python.*enhanced_search_mcp" || true
sleep 2

# 检查端口占用
check_and_free_port() {
    local port=$1
    local service=$2
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️ 端口 $port ($service) 被占用，释放中...${NC}"
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
}

check_and_free_port 8885 "SearXNG"
check_and_free_port 8881 "MCP Server"

# 创建日志目录
mkdir -p logs enhanced_search_mcp/logs

# 设置环境变量
export SEARXNG_PORT="8885"
export SEARXNG_BIND_ADDRESS="127.0.0.1"  # SearXNG只需本地访问
export SEARXNG_URL="http://localhost:8885"
export MCP_HOST="0.0.0.0"  # MCP Server监听所有接口
export MCP_PORT="8881"
export LOG_LEVEL="INFO"

echo -e "${BLUE}🔧 环境变量配置:${NC}"
echo -e "${GRAY}  SEARXNG_PORT=$SEARXNG_PORT${NC}"
echo -e "${GRAY}  SEARXNG_BIND_ADDRESS=$SEARXNG_BIND_ADDRESS${NC}"
echo -e "${GRAY}  MCP_HOST=$MCP_HOST${NC}"
echo -e "${GRAY}  MCP_PORT=$MCP_PORT${NC}"

# 启动SearXNG
start_searxng() {
    echo -e "${PURPLE}🔍 启动SearXNG搜索引擎...${NC}"
    cd "$PROJECT_DIR"

    # 检查SearXNG配置文件
    if [ ! -f "searx/settings.yml" ]; then
        echo -e "${YELLOW}⚠️ SearXNG配置文件不存在${NC}"
        if [ -f "searx/settings_robot.yml" ]; then
            cp searx/settings_robot.yml searx/settings.yml
            echo -e "${GREEN}✅ 使用默认配置文件${NC}"
        fi
    fi

    # 启动SearXNG (后台运行)
    nohup python -m searx.webapp > logs/searxng.log 2>&1 &
    SEARXNG_PID=$!
    echo -e "${GREEN}✅ SearXNG已启动 (PID: $SEARXNG_PID)${NC}"

    # 等待SearXNG启动
    echo -e "${YELLOW}⏳ 等待SearXNG启动...${NC}"
    for i in {1..30}; do
        if curl -s --connect-timeout 2 http://localhost:8885 > /dev/null 2>&1; then
            echo -e "${GREEN}✅ SearXNG启动成功${NC}"
            return 0
        fi
        sleep 1
        echo -n "."
    done

    echo -e "${RED}❌ SearXNG启动超时${NC}"
    echo -e "${YELLOW}查看日志: tail -20 logs/searxng.log${NC}"
    return 1
}

# 启动MCP服务器
start_mcp_server() {
    echo -e "${PURPLE}🤖 启动MCP服务器...${NC}"
    cd "$MCP_DIR"

    # 检查MCP服务器文件
    if [ ! -f "server.py" ]; then
        echo -e "${RED}❌ MCP服务器文件不存在: server.py${NC}"
        return 1
    fi

    # 启动MCP服务器 (后台运行)
    nohup python server.py > logs/mcp_server.log 2>&1 &
    MCP_PID=$!
    echo -e "${GREEN}✅ MCP服务器已启动 (PID: $MCP_PID)${NC}"

    # 等待MCP服务器启动
    echo -e "${YELLOW}⏳ 等待MCP服务器启动...${NC}"
    for i in {1..20}; do
        if curl -s --connect-timeout 2 http://localhost:8881/health > /dev/null 2>&1; then
            echo -e "${GREEN}✅ MCP服务器启动成功${NC}"
            return 0
        fi
        sleep 1
        echo -n "."
    done

    echo -e "${RED}❌ MCP服务器启动超时${NC}"
    echo -e "${YELLOW}查看日志: tail -20 enhanced_search_mcp/logs/mcp_server.log${NC}"
    return 1
}

# 清理函数
cleanup() {
    echo -e "\n${YELLOW}🛑 正在停止服务...${NC}"

    if [ ! -z "$SEARXNG_PID" ]; then
        kill $SEARXNG_PID 2>/dev/null || true
        echo -e "${GREEN}✅ SearXNG已停止${NC}"
    fi

    if [ ! -z "$MCP_PID" ]; then
        kill $MCP_PID 2>/dev/null || true
        echo -e "${GREEN}✅ MCP服务器已停止${NC}"
    fi

    # 强制清理端口
    lsof -ti:8885 | xargs kill -9 2>/dev/null || true
    lsof -ti:8881 | xargs kill -9 2>/dev/null || true

    echo -e "${CYAN}👋 所有服务已停止${NC}"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主启动流程
echo -e "${CYAN}🚀 开始启动服务...${NC}"

# 启动SearXNG
if start_searxng; then
    echo -e "${GREEN}✅ SearXNG启动完成${NC}"
else
    echo -e "${RED}❌ SearXNG启动失败${NC}"
    cleanup
    exit 1
fi

# 启动MCP服务器
if start_mcp_server; then
    echo -e "${GREEN}✅ MCP服务器启动完成${NC}"
else
    echo -e "${RED}❌ MCP服务器启动失败${NC}"
    cleanup
    exit 1
fi

# 获取WSL IP地址
WSL_IP=$(hostname -I | awk '{print $1}')

# 显示服务信息
echo -e "\n${CYAN}🎉 所有服务启动成功！${NC}"
echo -e "${CYAN}========================${NC}"
echo -e "${GREEN}🔍 SearXNG Web界面: http://localhost:8885${NC}"
echo -e "${GREEN}🤖 MCP Server (本地): http://localhost:8881${NC}"
echo -e "${GREEN}🤖 MCP Server (WSL): http://$WSL_IP:8881${NC}"
echo -e "${GREEN}🏥 健康检查: http://$WSL_IP:8881/health${NC}"
echo -e "${GREEN}🛠️ MCP工具列表: http://$WSL_IP:8881/tools${NC}"

echo -e "\n${BLUE}🐳 Docker LLM配置:${NC}"
echo -e "${GREEN}  MCP Server URL: http://host.docker.internal:8881${NC}"
echo -e "${GRAY}  Docker运行参数: --add-host host.docker.internal:host-gateway${NC}"

echo -e "\n${YELLOW}📝 日志文件:${NC}"
echo -e "${GRAY}  SearXNG: $PROJECT_DIR/logs/searxng.log${NC}"
echo -e "${GRAY}  MCP Server: $MCP_DIR/logs/mcp_server.log${NC}"

echo -e "\n${YELLOW}📋 管理命令:${NC}"
echo -e "${GRAY}  查看SearXNG日志: tail -f logs/searxng.log${NC}"
echo -e "${GRAY}  查看MCP日志: tail -f enhanced_search_mcp/logs/mcp_server.log${NC}"
echo -e "${GRAY}  测试SearXNG: curl http://localhost:8885${NC}"
echo -e "${GRAY}  测试MCP: curl http://localhost:8881/health${NC}"

echo -e "\n${YELLOW}按 Ctrl+C 停止所有服务${NC}"

# 保持脚本运行并监控服务状态
while true; do
    sleep 10

    # 检查SearXNG状态
    if ! kill -0 $SEARXNG_PID 2>/dev/null; then
        echo -e "${RED}❌ SearXNG进程已停止${NC}"
        cleanup
        exit 1
    fi

    # 检查MCP服务器状态
    if ! kill -0 $MCP_PID 2>/dev/null; then
        echo -e "${RED}❌ MCP服务器进程已停止${NC}"
        cleanup
        exit 1
    fi

    # 每分钟显示一次状态
    if [ $(($(date +%s) % 60)) -eq 0 ]; then
        echo -e "${BLUE}📊 服务运行中... SearXNG(PID:$SEARXNG_PID) MCP(PID:$MCP_PID)${NC}"
    fi
done
