[{"name": "mcp_server_primary", "type": "mcp_server", "url": "http://host.docker.internal:8881/sse", "protocol": "sse", "status": "active", "docker_config": {"extra_hosts": ["host.docker.internal:host-gateway"], "environment": {"MCP_SERVER_URL": "http://host.docker.internal:8881/sse"}}, "tools": ["enhanced_search", "basic_search", "get_search_stats", "get_system_info"]}, {"name": "mcp_server_direct_ip", "type": "mcp_server", "url": "http://**************:8881/sse", "protocol": "sse", "status": "backup", "docker_config": {"environment": {"MCP_SERVER_URL": "http://**************:8881/sse"}}, "note": "备用配置，使用WSL直接IP"}, {"name": "searxng_internal", "type": "search_engine", "url": "http://localhost:8885", "access": "internal_only", "status": "running", "note": "仅供MCP Server内部调用"}]