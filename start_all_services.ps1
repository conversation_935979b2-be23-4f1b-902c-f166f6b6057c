# 同时启动SearXNG和MCP Server的PowerShell脚本
# 通过WSL执行，支持完整的服务管理

param(
    [switch]$Stop,
    [switch]$Status,
    [switch]$Logs,
    [switch]$Test,
    [switch]$Help
)

function Show-Help {
    Write-Host "🚀 SearXNG + MCP Server 综合启动脚本" -ForegroundColor Cyan
    Write-Host "===================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "用法:" -ForegroundColor Yellow
    Write-Host "  .\start_all_services.ps1 [选项]" -ForegroundColor White
    Write-Host ""
    Write-Host "选项:" -ForegroundColor Yellow
    Write-Host "  (无参数)    启动所有服务" -ForegroundColor White
    Write-Host "  -Stop       停止所有服务" -ForegroundColor White
    Write-Host "  -Status     查看服务状态" -ForegroundColor White
    Write-Host "  -Logs       查看服务日志" -ForegroundColor White
    Write-Host "  -Test       测试服务连通性" -ForegroundColor White
    Write-Host "  -Help       显示此帮助信息" -ForegroundColor White
    Write-Host ""
    Write-Host "示例:" -ForegroundColor Yellow
    Write-Host "  .\start_all_services.ps1           # 启动所有服务" -ForegroundColor Gray
    Write-Host "  .\start_all_services.ps1 -Status   # 查看状态" -ForegroundColor Gray
    Write-Host "  .\start_all_services.ps1 -Test     # 测试连接" -ForegroundColor Gray
    Write-Host "  .\start_all_services.ps1 -Stop     # 停止服务" -ForegroundColor Gray
}

if ($Help) {
    Show-Help
    exit 0
}

Write-Host "🚀 SearXNG + MCP Server 管理脚本" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan

# 检查WSL是否可用
Write-Host "🔍 检查WSL环境..." -ForegroundColor Blue
try {
    $wslVersion = wsl --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ WSL运行正常" -ForegroundColor Green
    } else {
        throw "WSL命令执行失败"
    }
} catch {
    Write-Host "❌ WSL未安装或未运行" -ForegroundColor Red
    Write-Host "请确保WSL已安装并配置" -ForegroundColor Yellow
    exit 1
}

# 获取WSL IP地址
Write-Host "📍 获取WSL网络信息..." -ForegroundColor Blue
$wslIP = (wsl hostname -I).Trim().Split()[0]
Write-Host "WSL IP地址: $wslIP" -ForegroundColor Green

# 停止服务
if ($Stop) {
    Write-Host "🛑 停止所有服务..." -ForegroundColor Yellow
    wsl -e bash -c "pkill -f 'python.*searx.webapp' || true"
    wsl -e bash -c "pkill -f 'python.*server.py' || true"
    wsl -e bash -c "pkill -f 'python.*enhanced_search_mcp' || true"
    Start-Sleep -Seconds 3
    
    # 强制清理端口
    wsl -e bash -c "lsof -ti:8885 | xargs kill -9 2>/dev/null || true"
    wsl -e bash -c "lsof -ti:8881 | xargs kill -9 2>/dev/null || true"
    
    Write-Host "✅ 服务停止命令已发送" -ForegroundColor Green
    exit 0
}

# 查看服务状态
if ($Status) {
    Write-Host "📊 检查服务状态..." -ForegroundColor Blue
    
    Write-Host "`n🔍 SearXNG (端口8885):" -ForegroundColor Yellow
    $searxngProcess = wsl -e bash -c "pgrep -f 'python.*searx.webapp' || echo 'not_running'"
    if ($searxngProcess -ne "not_running") {
        Write-Host "✅ SearXNG正在运行 (PID: $searxngProcess)" -ForegroundColor Green
        
        # 测试连接
        try {
            $response = Invoke-WebRequest -Uri "http://$wslIP:8885" -TimeoutSec 3 -UseBasicParsing -ErrorAction Stop
            Write-Host "✅ SearXNG连接正常" -ForegroundColor Green
        } catch {
            Write-Host "⚠️ SearXNG连接异常" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ SearXNG未运行" -ForegroundColor Red
    }
    
    Write-Host "`n🤖 MCP Server (端口8881):" -ForegroundColor Yellow
    $mcpProcess = wsl -e bash -c "pgrep -f 'python.*server.py' || echo 'not_running'"
    if ($mcpProcess -ne "not_running") {
        Write-Host "✅ MCP Server正在运行 (PID: $mcpProcess)" -ForegroundColor Green
        
        # 测试连接
        try {
            $response = Invoke-WebRequest -Uri "http://$wslIP:8881/health" -TimeoutSec 3 -UseBasicParsing -ErrorAction Stop
            Write-Host "✅ MCP Server连接正常" -ForegroundColor Green
        } catch {
            Write-Host "⚠️ MCP Server连接异常" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ MCP Server未运行" -ForegroundColor Red
    }
    
    Write-Host "`n🔌 端口监听状态:" -ForegroundColor Yellow
    $portStatus = wsl -e bash -c "netstat -ln | grep ':888[15] ' || echo '未找到监听端口'"
    Write-Host $portStatus -ForegroundColor Gray
    
    exit 0
}

# 查看日志
if ($Logs) {
    Write-Host "📋 查看服务日志..." -ForegroundColor Blue
    Write-Host "按 Ctrl+C 退出日志查看" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "=== SearXNG日志 (最近20行) ===" -ForegroundColor Cyan
    wsl -e bash -c "tail -20 logs/searxng.log 2>/dev/null || echo '日志文件不存在'"
    
    Write-Host "`n=== MCP Server日志 (最近20行) ===" -ForegroundColor Cyan
    wsl -e bash -c "tail -20 enhanced_search_mcp/logs/mcp_server.log 2>/dev/null || echo '日志文件不存在'"
    
    Write-Host "`n📋 实时日志监控 (按Ctrl+C退出):" -ForegroundColor Yellow
    Write-Host "SearXNG日志: wsl -e bash -c 'tail -f logs/searxng.log'" -ForegroundColor Gray
    Write-Host "MCP日志: wsl -e bash -c 'tail -f enhanced_search_mcp/logs/mcp_server.log'" -ForegroundColor Gray
    
    exit 0
}

# 测试服务连通性
if ($Test) {
    Write-Host "🧪 测试服务连通性..." -ForegroundColor Blue
    
    # 测试SearXNG
    Write-Host "`n🔍 测试SearXNG:" -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri "http://$wslIP:8885" -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ SearXNG连接正常" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ SearXNG连接失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 测试MCP Server
    Write-Host "`n🤖 测试MCP Server:" -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri "http://$wslIP:8881/health" -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ MCP Server健康检查通过" -ForegroundColor Green
            $healthData = $response.Content | ConvertFrom-Json
            Write-Host "   状态: $($healthData.status)" -ForegroundColor Gray
        }
    } catch {
        Write-Host "❌ MCP Server连接失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 测试MCP工具
    Write-Host "`n🛠️ 测试MCP工具:" -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri "http://$wslIP:8881/tools" -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ MCP工具列表获取成功" -ForegroundColor Green
            $tools = $response.Content | ConvertFrom-Json
            Write-Host "   可用工具数量: $($tools.tools.Count)" -ForegroundColor Gray
        }
    } catch {
        Write-Host "❌ MCP工具列表获取失败" -ForegroundColor Red
    }
    
    # 测试Docker访问
    Write-Host "`n🐳 测试Docker访问:" -ForegroundColor Yellow
    try {
        $dockerVersion = docker version --format "{{.Server.Version}}" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Docker运行正常" -ForegroundColor Green
            
            $dockerTest = docker run --rm --add-host host.docker.internal:host-gateway alpine:latest sh -c "wget -q --spider http://host.docker.internal:8881/health && echo 'SUCCESS' || echo 'FAILED'" 2>$null
            
            if ($dockerTest -eq "SUCCESS") {
                Write-Host "✅ Docker可以访问MCP Server" -ForegroundColor Green
            } else {
                Write-Host "❌ Docker无法访问MCP Server" -ForegroundColor Red
            }
        } else {
            Write-Host "⚠️ Docker未运行" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ Docker测试失败" -ForegroundColor Red
    }
    
    exit 0
}

# 启动服务
Write-Host "🚀 启动所有服务..." -ForegroundColor Blue

# 检查启动脚本是否存在
if (-not (Test-Path "start_all_services.sh")) {
    Write-Host "❌ 启动脚本不存在: start_all_services.sh" -ForegroundColor Red
    Write-Host "请确保脚本文件在当前目录中" -ForegroundColor Yellow
    exit 1
}

# 设置脚本可执行权限
wsl -e bash -c "chmod +x start_all_services.sh"

# 停止可能运行的旧服务
Write-Host "🛑 清理旧服务..." -ForegroundColor Yellow
wsl -e bash -c "pkill -f 'python.*searx.webapp' || true"
wsl -e bash -c "pkill -f 'python.*server.py' || true"
Start-Sleep -Seconds 2

Write-Host "🚀 在WSL中启动服务..." -ForegroundColor Blue
Write-Host "注意: 服务将在WSL中后台运行" -ForegroundColor Yellow

# 在WSL中启动服务（后台运行）
$startCommand = @"
#!/bin/bash
cd `$(pwd)
nohup ./start_all_services.sh > service_startup.log 2>&1 &
echo `$! > service.pid
echo "服务启动中，PID: `$(cat service.pid)"
"@

$startCommand | wsl -e bash

# 等待服务启动
Write-Host "⏳ 等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# 检查服务状态
Write-Host "`n📊 验证服务状态:" -ForegroundColor Cyan

# 检查SearXNG
$searxngOK = $false
try {
    $response = Invoke-WebRequest -Uri "http://$wslIP:8885" -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ SearXNG运行正常 (http://$wslIP:8885)" -ForegroundColor Green
        $searxngOK = $true
    }
} catch {
    Write-Host "⚠️ SearXNG可能仍在启动中..." -ForegroundColor Yellow
}

# 检查MCP Server
$mcpOK = $false
try {
    $response = Invoke-WebRequest -Uri "http://$wslIP:8881/health" -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ MCP Server运行正常 (http://$wslIP:8881)" -ForegroundColor Green
        $mcpOK = $true
    }
} catch {
    Write-Host "⚠️ MCP Server可能仍在启动中..." -ForegroundColor Yellow
}

if ($searxngOK -and $mcpOK) {
    Write-Host "`n🎉 所有服务启动成功！" -ForegroundColor Green
} else {
    Write-Host "`n⚠️ 部分服务可能仍在启动中，请稍后检查" -ForegroundColor Yellow
    Write-Host "查看启动日志: wsl -e bash -c 'tail -20 service_startup.log'" -ForegroundColor Gray
}

Write-Host "`n📋 服务信息:" -ForegroundColor Cyan
Write-Host "🔍 SearXNG Web界面: http://$wslIP:8885" -ForegroundColor Blue
Write-Host "🤖 MCP Server: http://$wslIP:8881" -ForegroundColor Blue
Write-Host "🏥 健康检查: http://$wslIP:8881/health" -ForegroundColor Blue
Write-Host "🛠️ 工具列表: http://$wslIP:8881/tools" -ForegroundColor Blue

Write-Host "`n🐳 Docker LLM配置:" -ForegroundColor Yellow
Write-Host "MCP Server URL: http://host.docker.internal:8881" -ForegroundColor Green
Write-Host "Docker参数: --add-host host.docker.internal:host-gateway" -ForegroundColor Gray

Write-Host "`n📋 管理命令:" -ForegroundColor Yellow
Write-Host "  查看状态: .\start_all_services.ps1 -Status" -ForegroundColor Gray
Write-Host "  查看日志: .\start_all_services.ps1 -Logs" -ForegroundColor Gray
Write-Host "  测试连接: .\start_all_services.ps1 -Test" -ForegroundColor Gray
Write-Host "  停止服务: .\start_all_services.ps1 -Stop" -ForegroundColor Gray

Write-Host "`n💡 提示:" -ForegroundColor Yellow
Write-Host "- 服务在WSL中后台运行，关闭此窗口不会停止服务" -ForegroundColor Gray
Write-Host "- 如需停止服务，请使用 -Stop 参数" -ForegroundColor Gray
Write-Host "- 服务日志保存在WSL的项目目录中" -ForegroundColor Gray
